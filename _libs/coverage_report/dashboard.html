<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#13">Nzoom\Export\Factory\ExportFormatFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#11">Nzoom\Export\ExportActionFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#10">Nzoom\Export\Entity\ExportColumn</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#16">Nzoom\Export\DataFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">89%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">44310</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">5112</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#15">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">4556</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#10">Nzoom\Export\Entity\ExportValue</a></td><td class="text-right">3782</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#12">Nzoom\Export\Entity\ExportData</a></td><td class="text-right">3192</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">1482</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#11">Nzoom\Export\Entity\ExportHeader</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#11">Nzoom\Export\Entity\ExportRecord</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#11">Nzoom\Export\ExportActionFactory</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#13">Nzoom\Export\Factory\ExportFormatFactory</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="DataFactory.php.html#16">Nzoom\Export\DataFactory</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#10">Nzoom\Export\Entity\ExportColumn</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">27</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#70"><abbr title="Nzoom\Export\Entity\ExportValue::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#187"><abbr title="Nzoom\Export\Entity\ExportRecord::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#197"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#209"><abbr title="Nzoom\Export\Entity\ExportRecord::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#220"><abbr title="Nzoom\Export\Entity\ExportRecord::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#231"><abbr title="Nzoom\Export\Entity\ExportRecord::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#241"><abbr title="Nzoom\Export\Entity\ExportRecord::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#251"><abbr title="Nzoom\Export\Entity\ExportRecord::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#261"><abbr title="Nzoom\Export\Entity\ExportRecord::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#271"><abbr title="Nzoom\Export\Entity\ExportRecord::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#279"><abbr title="Nzoom\Export\Entity\ExportRecord::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#289"><abbr title="Nzoom\Export\Entity\ExportRecord::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#58"><abbr title="Nzoom\Export\Entity\ExportValue::getValidTypes">getValidTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#82"><abbr title="Nzoom\Export\Entity\ExportValue::getValue">getValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#163"><abbr title="Nzoom\Export\Entity\ExportRecord::getRawValues">getRawValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#92"><abbr title="Nzoom\Export\Entity\ExportValue::setValue">setValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#102"><abbr title="Nzoom\Export\Entity\ExportValue::getType">getType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#131"><abbr title="Nzoom\Export\Entity\ExportValue::getFormat">getFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#142"><abbr title="Nzoom\Export\Entity\ExportValue::setFormat">setFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#153"><abbr title="Nzoom\Export\Entity\ExportValue::isNull">isNull</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#55"><abbr title="Nzoom\Export\ExportActionFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#85"><abbr title="Nzoom\Export\ExportActionFactory::setModelName">setModelName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#97"><abbr title="Nzoom\Export\ExportActionFactory::setModelFactoryName">setModelFactoryName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#110"><abbr title="Nzoom\Export\ExportActionFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#175"><abbr title="Nzoom\Export\Entity\ExportRecord::getFormattedValues">getFormattedValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#153"><abbr title="Nzoom\Export\Entity\ExportRecord::hasValue">hasValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#141"><abbr title="Nzoom\Export\ExportActionFactory::prepareExportOptions">prepareExportOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#198"><abbr title="Nzoom\Export\Entity\ExportHeader::getVarNames">getVarNames</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#79"><abbr title="Nzoom\Export\Entity\ExportHeader::hasColumn">hasColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#89"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#100"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnAt">getColumnAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#125"><abbr title="Nzoom\Export\Entity\ExportHeader::getBackgroundColor">getBackgroundColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#135"><abbr title="Nzoom\Export\Entity\ExportHeader::setBackgroundColor">setBackgroundColor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#145"><abbr title="Nzoom\Export\Entity\ExportHeader::getStyles">getStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#155"><abbr title="Nzoom\Export\Entity\ExportHeader::setStyles">setStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#166"><abbr title="Nzoom\Export\Entity\ExportHeader::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#176"><abbr title="Nzoom\Export\Entity\ExportHeader::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#186"><abbr title="Nzoom\Export\Entity\ExportHeader::getLabels">getLabels</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#210"><abbr title="Nzoom\Export\Entity\ExportHeader::getTypes">getTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#138"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#264"><abbr title="Nzoom\Export\Entity\ExportHeader::validateRecord">validateRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#291"><abbr title="Nzoom\Export\Entity\ExportHeader::rewind">rewind</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#301"><abbr title="Nzoom\Export\Entity\ExportHeader::current">current</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#311"><abbr title="Nzoom\Export\Entity\ExportHeader::key">key</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#319"><abbr title="Nzoom\Export\Entity\ExportHeader::next">next</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#329"><abbr title="Nzoom\Export\Entity\ExportHeader::valid">valid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#38"><abbr title="Nzoom\Export\Entity\ExportRecord::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#52"><abbr title="Nzoom\Export\Entity\ExportRecord::addValue">addValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#71"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#98"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#116"><abbr title="Nzoom\Export\Entity\ExportRecord::getValues">getValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#127"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueAt">getValueAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#122"><abbr title="Nzoom\Export\ExportActionFactory::createExportAction">createExportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#177"><abbr title="Nzoom\Export\ExportActionFactory::initializeFilterVisibility">initializeFilterVisibility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#402"><abbr title="Nzoom\Export\Entity\ExportData::getLazyIterator">getLazyIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#278"><abbr title="Nzoom\Export\Streamer\FileStreamer::getFilename">getFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#85"><abbr title="Nzoom\Export\Streamer\FileStreamer::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#96"><abbr title="Nzoom\Export\Streamer\FileStreamer::setTimeIncrement">setTimeIncrement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#107"><abbr title="Nzoom\Export\Streamer\FileStreamer::setCacheExpires">setCacheExpires</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#118"><abbr title="Nzoom\Export\Streamer\FileStreamer::setETag">setETag</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#129"><abbr title="Nzoom\Export\Streamer\FileStreamer::setLastModified">setLastModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#243"><abbr title="Nzoom\Export\Streamer\FileStreamer::isClientConnected">isClientConnected</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#288"><abbr title="Nzoom\Export\Streamer\FileStreamer::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#60"><abbr title="Nzoom\Export\Streamer\FileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#140"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#150"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#110"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getChunkSize">getChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#136"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#227"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapterFromFilename">createAdapterFromFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#194"><abbr title="Nzoom\Export\ExportActionFactory::firstOrZero">firstOrZero</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#209"><abbr title="Nzoom\Export\ExportActionFactory::getPluginOptions">getPluginOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#280"><abbr title="Nzoom\Export\ExportActionFactory::buildBaseExportOptions">buildBaseExportOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#329"><abbr title="Nzoom\Export\ExportActionFactory::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#364"><abbr title="Nzoom\Export\ExportActionFactory::getGroupTablesOptions">getGroupTablesOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#397"><abbr title="Nzoom\Export\ExportActionFactory::getDelimiterOptions">getDelimiterOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#69"><abbr title="Nzoom\Export\ExportService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#84"><abbr title="Nzoom\Export\ExportService::setModelName">setModelName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#96"><abbr title="Nzoom\Export\ExportService::setModelFactoryName">setModelFactoryName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#110"><abbr title="Nzoom\Export\ExportService::createExportAction">createExportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#127"><abbr title="Nzoom\Export\ExportService::createExportData">createExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#142"><abbr title="Nzoom\Export\ExportService::createGeneratorFileStreamer">createGeneratorFileStreamer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#213"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isFormatSupported">isFormatSupported</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#247"><abbr title="Nzoom\Export\ExportService::setFormatFactory">setFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#273"><abbr title="Nzoom\Export\ExportService::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#284"><abbr title="Nzoom\Export\ExportService::isFormatSupported">isFormatSupported</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#44"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#60"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapter">createAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#94"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getAdapterClass">getAdapterClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#112"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::discoverAdapters">discoverAdapters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#148"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getClassNameFromFile">getClassNameFromFile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#186"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#44"><abbr title="Nzoom\Export\Entity\ExportHeader::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#388"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#57"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::setConfiguration">setConfiguration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#260"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportData">processExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#302"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::styleHeaderRow">styleHeaderRow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#555"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomDateFormat">convertCustomDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#941"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#966"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#974"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#982"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#990"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#30"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#368"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#151"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#40"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#75"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::extractCsvOptions">extractCsvOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::writeCsvContent">writeCsvContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#187"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#136"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createSpreadsheet">createSpreadsheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#205"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatValueForCsv">formatValueForCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#246"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#272"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDecimalPlaces">getDecimalPlaces</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#288"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#315"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#333"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#341"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#350"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#358"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#366"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#374"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#65"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::extractJsonOptions">extractJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::writeJsonContent">writeJsonContent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#129"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareJsonData">prepareJsonData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#113"><abbr title="Nzoom\Export\Entity\ExportData::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#171"><abbr title="Nzoom\Export\Entity\ExportColumn::getWidth">getWidth</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#181"><abbr title="Nzoom\Export\Entity\ExportColumn::setWidth">setWidth</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#191"><abbr title="Nzoom\Export\Entity\ExportColumn::getStyles">getStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#201"><abbr title="Nzoom\Export\Entity\ExportColumn::setStyles">setStyles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#212"><abbr title="Nzoom\Export\Entity\ExportColumn::addStyle">addStyle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#223"><abbr title="Nzoom\Export\Entity\ExportColumn::validateValue">validateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#235"><abbr title="Nzoom\Export\Entity\ExportColumn::createValue">createValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#50"><abbr title="Nzoom\Export\Entity\ExportData::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#64"><abbr title="Nzoom\Export\Entity\ExportData::setRecordProvider">setRecordProvider</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#81"><abbr title="Nzoom\Export\Entity\ExportData::isLazy">isLazy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#91"><abbr title="Nzoom\Export\Entity\ExportData::getPageSize">getPageSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#102"><abbr title="Nzoom\Export\Entity\ExportData::setPageSize">setPageSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#123"><abbr title="Nzoom\Export\Entity\ExportData::setHeader">setHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#150"><abbr title="Nzoom\Export\Entity\ExportColumn::getFormat">getFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#133"><abbr title="Nzoom\Export\Entity\ExportData::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#143"><abbr title="Nzoom\Export\Entity\ExportData::setMetadata">setMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#155"><abbr title="Nzoom\Export\Entity\ExportData::getMetadataValue">getMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#166"><abbr title="Nzoom\Export\Entity\ExportData::setMetadataValue">setMetadataValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#180"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#199"><abbr title="Nzoom\Export\Entity\ExportData::getRecords">getRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#220"><abbr title="Nzoom\Export\Entity\ExportData::getRecordAt">getRecordAt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#242"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#259"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#275"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#291"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#339"><abbr title="Nzoom\Export\Entity\ExportData::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#350"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#161"><abbr title="Nzoom\Export\Entity\ExportColumn::setFormat">setFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#148"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareArrayStructure">prepareArrayStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#120"><abbr title="Nzoom\Export\Entity\ExportColumn::getType">getType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#176"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareObjectStructure">prepareObjectStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#201"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareNestedStructure">prepareNestedStructure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#233"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#259"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatValueForJson">formatValueForJson</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#297"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#324"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::addMetadata">addMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#338"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMetadata">getMetadata</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#373"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#428"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getSupportedExtensions">getSupportedExtensions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#436"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#445"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::supportsFormat">supportsFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#461"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatName">getFormatName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#469"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#33"><abbr title="Nzoom\Export\DataFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#45"><abbr title="Nzoom\Export\DataFactory::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#71"><abbr title="Nzoom\Export\DataFactory::createHeaderFromOutlook">createHeaderFromOutlook</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#101"><abbr title="Nzoom\Export\DataFactory::processModelsChunk">processModelsChunk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#115"><abbr title="Nzoom\Export\DataFactory::createRecordFromModel">createRecordFromModel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#149"><abbr title="Nzoom\Export\DataFactory::mapFieldTypeToExportType">mapFieldTypeToExportType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#178"><abbr title="Nzoom\Export\DataFactory::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#192"><abbr title="Nzoom\Export\DataFactory::createStreaming">createStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFactory.php.html#239"><abbr title="Nzoom\Export\DataFactory::createCursorStreaming">createCursorStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#53"><abbr title="Nzoom\Export\Entity\ExportColumn::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#74"><abbr title="Nzoom\Export\Entity\ExportColumn::getVarName">getVarName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#100"><abbr title="Nzoom\Export\Entity\ExportColumn::getLabel">getLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#110"><abbr title="Nzoom\Export\Entity\ExportColumn::setLabel">setLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">40%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">6972</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#163"><abbr title="Nzoom\Export\Entity\ExportValue::validate">validate</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#291"><abbr title="Nzoom\Export\Entity\ExportData::sortByColumn">sortByColumn</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#196"><abbr title="Nzoom\Export\Entity\ExportValue::getFormattedValue">getFormattedValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#205"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatValueForCsv">formatValueForCsv</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#75"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::extractCsvOptions">extractCsvOptions</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#259"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatValueForJson">formatValueForJson</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#233"><abbr title="Nzoom\Export\Entity\ExportValue::__toString">__toString</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#65"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::extractJsonOptions">extractJsonOptions</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#297"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#151"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#246"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::formatDateValue">formatDateValue</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#373"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#201"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareNestedStructure">prepareNestedStructure</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::writeJsonContent">writeJsonContent</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#223"><abbr title="Nzoom\Export\Entity\ExportHeader::reorderColumns">reorderColumns</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#315"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#106"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::writeCsvContent">writeCsvContent</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#112"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::discoverAdapters">discoverAdapters</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#129"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareJsonData">prepareJsonData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#148"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareArrayStructure">prepareArrayStructure</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#264"><abbr title="Nzoom\Export\Entity\ExportHeader::validateRecord">validateRecord</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#180"><abbr title="Nzoom\Export\Entity\ExportData::addRecord">addRecord</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#209"><abbr title="Nzoom\Export\ExportActionFactory::getPluginOptions">getPluginOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DataFactory.php.html#71"><abbr title="Nzoom\Export\DataFactory::createHeaderFromOutlook">createHeaderFromOutlook</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DataFactory.php.html#239"><abbr title="Nzoom\Export\DataFactory::createCursorStreaming">createCursorStreaming</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DataFactory.php.html#115"><abbr title="Nzoom\Export\DataFactory::createRecordFromModel">createRecordFromModel</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#280"><abbr title="Nzoom\Export\ExportActionFactory::buildBaseExportOptions">buildBaseExportOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#71"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueAt">setValueAt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#30"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#40"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#148"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getClassNameFromFile">getClassNameFromFile</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#220"><abbr title="Nzoom\Export\Entity\ExportData::getRecordAt">getRecordAt</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#402"><abbr title="Nzoom\Export\Entity\ExportData::getLazyIterator">getLazyIterator</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#288"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Entity/ExportValue.php.html#113"><abbr title="Nzoom\Export\Entity\ExportValue::setType">setType</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#186"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#350"><abbr title="Nzoom\Export\Entity\ExportData::validate">validate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#368"><abbr title="Nzoom\Export\Entity\ExportData::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#55"><abbr title="Nzoom\Export\ExportActionFactory::__construct">__construct</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#199"><abbr title="Nzoom\Export\Entity\ExportData::getRecords">getRecords</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#177"><abbr title="Nzoom\Export\ExportActionFactory::initializeFilterVisibility">initializeFilterVisibility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#94"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::getAdapterClass">getAdapterClass</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#338"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMetadata">getMetadata</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#233"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#60"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapter">createAdapter</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#227"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::createAdapterFromFilename">createAdapterFromFilename</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#131"><abbr title="Nzoom\Export\Entity\ExportColumn::setType">setType</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#187"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#272"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDecimalPlaces">getDecimalPlaces</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#176"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::prepareObjectStructure">prepareObjectStructure</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#45"><abbr title="Nzoom\Export\DataFactory::__invoke">__invoke</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#101"><abbr title="Nzoom\Export\DataFactory::processModelsChunk">processModelsChunk</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFactory.php.html#192"><abbr title="Nzoom\Export\DataFactory::createStreaming">createStreaming</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportColumn.php.html#86"><abbr title="Nzoom\Export\Entity\ExportColumn::setVarName">setVarName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#242"><abbr title="Nzoom\Export\Entity\ExportData::count">count</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#259"><abbr title="Nzoom\Export\Entity\ExportData::createRecord">createRecord</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#275"><abbr title="Nzoom\Export\Entity\ExportData::isEmpty">isEmpty</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportData.php.html#388"><abbr title="Nzoom\Export\Entity\ExportData::getIterator">getIterator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#57"><abbr title="Nzoom\Export\Entity\ExportHeader::addColumn">addColumn</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportHeader.php.html#111"><abbr title="Nzoom\Export\Entity\ExportHeader::getColumnByVarName">getColumnByVarName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#52"><abbr title="Nzoom\Export\Entity\ExportRecord::addValue">addValue</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#98"><abbr title="Nzoom\Export\Entity\ExportRecord::setValueByColumnName">setValueByColumnName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Entity/ExportRecord.php.html#138"><abbr title="Nzoom\Export\Entity\ExportRecord::getValueByColumnName">getValueByColumnName</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#194"><abbr title="Nzoom\Export\ExportActionFactory::firstOrZero">firstOrZero</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#329"><abbr title="Nzoom\Export\ExportActionFactory::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportActionFactory.php.html#397"><abbr title="Nzoom\Export\ExportActionFactory::getDelimiterOptions">getDelimiterOptions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">4</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 16 6:43:56 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([16,0,0,0,0,0,0,0,0,1,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([250,0,0,0,0,1,0,0,0,0,0,13], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,38,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[0,67,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[0,210,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#25\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[0,71,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"],[0,21,"<a href=\"DataFactory.php.html#16\">Nzoom\\Export\\DataFactory<\/a>"],[0,18,"<a href=\"Entity\/ExportColumn.php.html#10\">Nzoom\\Export\\Entity\\ExportColumn<\/a>"],[0,56,"<a href=\"Entity\/ExportData.php.html#12\">Nzoom\\Export\\Entity\\ExportData<\/a>"],[0,33,"<a href=\"Entity\/ExportHeader.php.html#11\">Nzoom\\Export\\Entity\\ExportHeader<\/a>"],[0,27,"<a href=\"Entity\/ExportRecord.php.html#11\">Nzoom\\Export\\Entity\\ExportRecord<\/a>"],[0,61,"<a href=\"Entity\/ExportValue.php.html#10\">Nzoom\\Export\\Entity\\ExportValue<\/a>"],[0,26,"<a href=\"ExportActionFactory.php.html#11\">Nzoom\\Export\\ExportActionFactory<\/a>"],[0,29,"<a href=\"ExportService.php.html#13\">Nzoom\\Export\\ExportService<\/a>"],[0,25,"<a href=\"Factory\/ExportFormatFactory.php.html#13\">Nzoom\\Export\\Factory\\ExportFormatFactory<\/a>"],[0,23,"<a href=\"Streamer\/FileStreamer.php.html#12\">Nzoom\\Export\\Streamer\\FileStreamer<\/a>"],[0,20,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer<\/a>"],[0,18,"<a href=\"Streamer\/PointerFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\PointerFileStreamer<\/a>"],[89.13043478260869,27,"<a href=\"Streamer\/StreamHeaders.php.html#11\">Nzoom\\Export\\Streamer\\StreamHeaders<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[0,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[0,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[0,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[0,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#40\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[0,11,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#75\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::extractCsvOptions<\/a>"],[0,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#106\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::writeCsvContent<\/a>"],[0,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#151\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportDataRecords<\/a>"],[0,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#187\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportRecord<\/a>"],[0,12,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#205\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatValueForCsv<\/a>"],[0,7,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#246\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatDateValue<\/a>"],[0,2,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#272\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDecimalPlaces<\/a>"],[0,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#288\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[0,6,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#315\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#333\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#341\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#350\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#358\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#366\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#374\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#54\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[0,11,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#104\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#136\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#165\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#188\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#239\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#260\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#287\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#302\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#318\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#367\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[0,17,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#392\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[0,15,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#481\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#532\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#555\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#586\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#604\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#628\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#665\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#722\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#742\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#763\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[0,83,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#793\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#895\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#918\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#941\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#949\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#966\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#974\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#982\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#990\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[0,4,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#30\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#65\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::extractJsonOptions<\/a>"],[0,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::writeJsonContent<\/a>"],[0,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#129\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareJsonData<\/a>"],[0,5,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#148\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareArrayStructure<\/a>"],[0,2,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#176\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareObjectStructure<\/a>"],[0,6,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#201\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareNestedStructure<\/a>"],[0,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#233\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::processExportRecord<\/a>"],[0,9,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#259\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatValueForJson<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatDateValue<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#324\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::addMetadata<\/a>"],[0,3,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#338\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMetadata<\/a>"],[0,7,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#373\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#428\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#445\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#461\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#469\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"],[0,1,"<a href=\"DataFactory.php.html#33\">Nzoom\\Export\\DataFactory::__construct<\/a>"],[0,2,"<a href=\"DataFactory.php.html#45\">Nzoom\\Export\\DataFactory::__invoke<\/a>"],[0,4,"<a href=\"DataFactory.php.html#71\">Nzoom\\Export\\DataFactory::createHeaderFromOutlook<\/a>"],[0,2,"<a href=\"DataFactory.php.html#101\">Nzoom\\Export\\DataFactory::processModelsChunk<\/a>"],[0,4,"<a href=\"DataFactory.php.html#115\">Nzoom\\Export\\DataFactory::createRecordFromModel<\/a>"],[0,1,"<a href=\"DataFactory.php.html#149\">Nzoom\\Export\\DataFactory::mapFieldTypeToExportType<\/a>"],[0,1,"<a href=\"DataFactory.php.html#178\">Nzoom\\Export\\DataFactory::setChunkSize<\/a>"],[0,2,"<a href=\"DataFactory.php.html#192\">Nzoom\\Export\\DataFactory::createStreaming<\/a>"],[0,4,"<a href=\"DataFactory.php.html#239\">Nzoom\\Export\\DataFactory::createCursorStreaming<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#53\">Nzoom\\Export\\Entity\\ExportColumn::__construct<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#74\">Nzoom\\Export\\Entity\\ExportColumn::getVarName<\/a>"],[0,2,"<a href=\"Entity\/ExportColumn.php.html#86\">Nzoom\\Export\\Entity\\ExportColumn::setVarName<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#100\">Nzoom\\Export\\Entity\\ExportColumn::getLabel<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#110\">Nzoom\\Export\\Entity\\ExportColumn::setLabel<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#120\">Nzoom\\Export\\Entity\\ExportColumn::getType<\/a>"],[0,2,"<a href=\"Entity\/ExportColumn.php.html#131\">Nzoom\\Export\\Entity\\ExportColumn::setType<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#150\">Nzoom\\Export\\Entity\\ExportColumn::getFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#161\">Nzoom\\Export\\Entity\\ExportColumn::setFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#171\">Nzoom\\Export\\Entity\\ExportColumn::getWidth<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#181\">Nzoom\\Export\\Entity\\ExportColumn::setWidth<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#191\">Nzoom\\Export\\Entity\\ExportColumn::getStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#201\">Nzoom\\Export\\Entity\\ExportColumn::setStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#212\">Nzoom\\Export\\Entity\\ExportColumn::addStyle<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#223\">Nzoom\\Export\\Entity\\ExportColumn::validateValue<\/a>"],[0,1,"<a href=\"Entity\/ExportColumn.php.html#235\">Nzoom\\Export\\Entity\\ExportColumn::createValue<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#50\">Nzoom\\Export\\Entity\\ExportData::__construct<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#64\">Nzoom\\Export\\Entity\\ExportData::setRecordProvider<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#81\">Nzoom\\Export\\Entity\\ExportData::isLazy<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#91\">Nzoom\\Export\\Entity\\ExportData::getPageSize<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#102\">Nzoom\\Export\\Entity\\ExportData::setPageSize<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#113\">Nzoom\\Export\\Entity\\ExportData::getHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#123\">Nzoom\\Export\\Entity\\ExportData::setHeader<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#133\">Nzoom\\Export\\Entity\\ExportData::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#143\">Nzoom\\Export\\Entity\\ExportData::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#155\">Nzoom\\Export\\Entity\\ExportData::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#166\">Nzoom\\Export\\Entity\\ExportData::setMetadataValue<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#180\">Nzoom\\Export\\Entity\\ExportData::addRecord<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#199\">Nzoom\\Export\\Entity\\ExportData::getRecords<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#220\">Nzoom\\Export\\Entity\\ExportData::getRecordAt<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#242\">Nzoom\\Export\\Entity\\ExportData::count<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#259\">Nzoom\\Export\\Entity\\ExportData::createRecord<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#275\">Nzoom\\Export\\Entity\\ExportData::isEmpty<\/a>"],[0,15,"<a href=\"Entity\/ExportData.php.html#291\">Nzoom\\Export\\Entity\\ExportData::sortByColumn<\/a>"],[0,1,"<a href=\"Entity\/ExportData.php.html#339\">Nzoom\\Export\\Entity\\ExportData::filter<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#350\">Nzoom\\Export\\Entity\\ExportData::validate<\/a>"],[0,3,"<a href=\"Entity\/ExportData.php.html#368\">Nzoom\\Export\\Entity\\ExportData::toArray<\/a>"],[0,2,"<a href=\"Entity\/ExportData.php.html#388\">Nzoom\\Export\\Entity\\ExportData::getIterator<\/a>"],[0,4,"<a href=\"Entity\/ExportData.php.html#402\">Nzoom\\Export\\Entity\\ExportData::getLazyIterator<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#44\">Nzoom\\Export\\Entity\\ExportHeader::__construct<\/a>"],[0,2,"<a href=\"Entity\/ExportHeader.php.html#57\">Nzoom\\Export\\Entity\\ExportHeader::addColumn<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#79\">Nzoom\\Export\\Entity\\ExportHeader::hasColumn<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#89\">Nzoom\\Export\\Entity\\ExportHeader::getColumns<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#100\">Nzoom\\Export\\Entity\\ExportHeader::getColumnAt<\/a>"],[0,2,"<a href=\"Entity\/ExportHeader.php.html#111\">Nzoom\\Export\\Entity\\ExportHeader::getColumnByVarName<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#125\">Nzoom\\Export\\Entity\\ExportHeader::getBackgroundColor<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#135\">Nzoom\\Export\\Entity\\ExportHeader::setBackgroundColor<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#145\">Nzoom\\Export\\Entity\\ExportHeader::getStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#155\">Nzoom\\Export\\Entity\\ExportHeader::setStyles<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#166\">Nzoom\\Export\\Entity\\ExportHeader::addStyle<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#176\">Nzoom\\Export\\Entity\\ExportHeader::count<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#186\">Nzoom\\Export\\Entity\\ExportHeader::getLabels<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#198\">Nzoom\\Export\\Entity\\ExportHeader::getVarNames<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#210\">Nzoom\\Export\\Entity\\ExportHeader::getTypes<\/a>"],[0,6,"<a href=\"Entity\/ExportHeader.php.html#223\">Nzoom\\Export\\Entity\\ExportHeader::reorderColumns<\/a>"],[0,5,"<a href=\"Entity\/ExportHeader.php.html#264\">Nzoom\\Export\\Entity\\ExportHeader::validateRecord<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#291\">Nzoom\\Export\\Entity\\ExportHeader::rewind<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#301\">Nzoom\\Export\\Entity\\ExportHeader::current<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#311\">Nzoom\\Export\\Entity\\ExportHeader::key<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#319\">Nzoom\\Export\\Entity\\ExportHeader::next<\/a>"],[0,1,"<a href=\"Entity\/ExportHeader.php.html#329\">Nzoom\\Export\\Entity\\ExportHeader::valid<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#38\">Nzoom\\Export\\Entity\\ExportRecord::__construct<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#52\">Nzoom\\Export\\Entity\\ExportRecord::addValue<\/a>"],[0,4,"<a href=\"Entity\/ExportRecord.php.html#71\">Nzoom\\Export\\Entity\\ExportRecord::setValueAt<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#98\">Nzoom\\Export\\Entity\\ExportRecord::setValueByColumnName<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#116\">Nzoom\\Export\\Entity\\ExportRecord::getValues<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#127\">Nzoom\\Export\\Entity\\ExportRecord::getValueAt<\/a>"],[0,2,"<a href=\"Entity\/ExportRecord.php.html#138\">Nzoom\\Export\\Entity\\ExportRecord::getValueByColumnName<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#153\">Nzoom\\Export\\Entity\\ExportRecord::hasValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#163\">Nzoom\\Export\\Entity\\ExportRecord::getRawValues<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#175\">Nzoom\\Export\\Entity\\ExportRecord::getFormattedValues<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#187\">Nzoom\\Export\\Entity\\ExportRecord::getMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#197\">Nzoom\\Export\\Entity\\ExportRecord::setMetadata<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#209\">Nzoom\\Export\\Entity\\ExportRecord::getMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#220\">Nzoom\\Export\\Entity\\ExportRecord::setMetadataValue<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#231\">Nzoom\\Export\\Entity\\ExportRecord::validate<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#241\">Nzoom\\Export\\Entity\\ExportRecord::count<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#251\">Nzoom\\Export\\Entity\\ExportRecord::rewind<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#261\">Nzoom\\Export\\Entity\\ExportRecord::current<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#271\">Nzoom\\Export\\Entity\\ExportRecord::key<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#279\">Nzoom\\Export\\Entity\\ExportRecord::next<\/a>"],[0,1,"<a href=\"Entity\/ExportRecord.php.html#289\">Nzoom\\Export\\Entity\\ExportRecord::valid<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#58\">Nzoom\\Export\\Entity\\ExportValue::getValidTypes<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#70\">Nzoom\\Export\\Entity\\ExportValue::__construct<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#82\">Nzoom\\Export\\Entity\\ExportValue::getValue<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#92\">Nzoom\\Export\\Entity\\ExportValue::setValue<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#102\">Nzoom\\Export\\Entity\\ExportValue::getType<\/a>"],[0,3,"<a href=\"Entity\/ExportValue.php.html#113\">Nzoom\\Export\\Entity\\ExportValue::setType<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#131\">Nzoom\\Export\\Entity\\ExportValue::getFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#142\">Nzoom\\Export\\Entity\\ExportValue::setFormat<\/a>"],[0,1,"<a href=\"Entity\/ExportValue.php.html#153\">Nzoom\\Export\\Entity\\ExportValue::isNull<\/a>"],[0,27,"<a href=\"Entity\/ExportValue.php.html#163\">Nzoom\\Export\\Entity\\ExportValue::validate<\/a>"],[0,15,"<a href=\"Entity\/ExportValue.php.html#196\">Nzoom\\Export\\Entity\\ExportValue::getFormattedValue<\/a>"],[0,8,"<a href=\"Entity\/ExportValue.php.html#233\">Nzoom\\Export\\Entity\\ExportValue::__toString<\/a>"],[0,3,"<a href=\"ExportActionFactory.php.html#55\">Nzoom\\Export\\ExportActionFactory::__construct<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#85\">Nzoom\\Export\\ExportActionFactory::setModelName<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#97\">Nzoom\\Export\\ExportActionFactory::setModelFactoryName<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#110\">Nzoom\\Export\\ExportActionFactory::__invoke<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#122\">Nzoom\\Export\\ExportActionFactory::createExportAction<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#141\">Nzoom\\Export\\ExportActionFactory::prepareExportOptions<\/a>"],[0,3,"<a href=\"ExportActionFactory.php.html#177\">Nzoom\\Export\\ExportActionFactory::initializeFilterVisibility<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#194\">Nzoom\\Export\\ExportActionFactory::firstOrZero<\/a>"],[0,4,"<a href=\"ExportActionFactory.php.html#209\">Nzoom\\Export\\ExportActionFactory::getPluginOptions<\/a>"],[0,4,"<a href=\"ExportActionFactory.php.html#280\">Nzoom\\Export\\ExportActionFactory::buildBaseExportOptions<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#329\">Nzoom\\Export\\ExportActionFactory::getFormatOptions<\/a>"],[0,1,"<a href=\"ExportActionFactory.php.html#364\">Nzoom\\Export\\ExportActionFactory::getGroupTablesOptions<\/a>"],[0,2,"<a href=\"ExportActionFactory.php.html#397\">Nzoom\\Export\\ExportActionFactory::getDelimiterOptions<\/a>"],[0,1,"<a href=\"ExportService.php.html#69\">Nzoom\\Export\\ExportService::__construct<\/a>"],[0,1,"<a href=\"ExportService.php.html#84\">Nzoom\\Export\\ExportService::setModelName<\/a>"],[0,1,"<a href=\"ExportService.php.html#96\">Nzoom\\Export\\ExportService::setModelFactoryName<\/a>"],[0,1,"<a href=\"ExportService.php.html#110\">Nzoom\\Export\\ExportService::createExportAction<\/a>"],[0,1,"<a href=\"ExportService.php.html#127\">Nzoom\\Export\\ExportService::createExportData<\/a>"],[0,1,"<a href=\"ExportService.php.html#142\">Nzoom\\Export\\ExportService::createGeneratorFileStreamer<\/a>"],[0,3,"<a href=\"ExportService.php.html#156\">Nzoom\\Export\\ExportService::export<\/a>"],[0,2,"<a href=\"ExportService.php.html#181\">Nzoom\\Export\\ExportService::createTempStream<\/a>"],[0,3,"<a href=\"ExportService.php.html#201\">Nzoom\\Export\\ExportService::streamToBrowser<\/a>"],[0,2,"<a href=\"ExportService.php.html#232\">Nzoom\\Export\\ExportService::getFormatFactory<\/a>"],[0,1,"<a href=\"ExportService.php.html#247\">Nzoom\\Export\\ExportService::setFormatFactory<\/a>"],[0,2,"<a href=\"ExportService.php.html#259\">Nzoom\\Export\\ExportService::getAdapter<\/a>"],[0,1,"<a href=\"ExportService.php.html#273\">Nzoom\\Export\\ExportService::getSupportedFormats<\/a>"],[0,1,"<a href=\"ExportService.php.html#284\">Nzoom\\Export\\ExportService::isFormatSupported<\/a>"],[0,4,"<a href=\"ExportService.php.html#296\">Nzoom\\Export\\ExportService::getExportFilename<\/a>"],[0,4,"<a href=\"ExportService.php.html#326\">Nzoom\\Export\\ExportService::handleExportError<\/a>"],[0,1,"<a href=\"Factory\/ExportFormatFactory.php.html#44\">Nzoom\\Export\\Factory\\ExportFormatFactory::__construct<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#60\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapter<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#94\">Nzoom\\Export\\Factory\\ExportFormatFactory::getAdapterClass<\/a>"],[0,6,"<a href=\"Factory\/ExportFormatFactory.php.html#112\">Nzoom\\Export\\Factory\\ExportFormatFactory::discoverAdapters<\/a>"],[0,4,"<a href=\"Factory\/ExportFormatFactory.php.html#148\">Nzoom\\Export\\Factory\\ExportFormatFactory::getClassNameFromFile<\/a>"],[0,2,"<a href=\"Factory\/ExportFormatFactory.php.html#171\">Nzoom\\Export\\Factory\\ExportFormatFactory::isValidAdapter<\/a>"],[0,3,"<a href=\"Factory\/ExportFormatFactory.php.html#186\">Nzoom\\Export\\Factory\\ExportFormatFactory::getSupportedFormats<\/a>"],[0,1,"<a href=\"Factory\/ExportFormatFactory.php.html#213\">Nzoom\\Export\\Factory\\ExportFormatFactory::isFormatSupported<\/a>"],[0,2,"<a href=\"Factory\/ExportFormatFactory.php.html#227\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapterFromFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#60\">Nzoom\\Export\\Streamer\\FileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\FileStreamer::getHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#85\">Nzoom\\Export\\Streamer\\FileStreamer::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#96\">Nzoom\\Export\\Streamer\\FileStreamer::setTimeIncrement<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#107\">Nzoom\\Export\\Streamer\\FileStreamer::setCacheExpires<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#118\">Nzoom\\Export\\Streamer\\FileStreamer::setETag<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#129\">Nzoom\\Export\\Streamer\\FileStreamer::setLastModified<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#142\">Nzoom\\Export\\Streamer\\FileStreamer::stream<\/a>"],[100,0,"<a href=\"Streamer\/FileStreamer.php.html#168\">Nzoom\\Export\\Streamer\\FileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#175\">Nzoom\\Export\\Streamer\\FileStreamer::prepareForStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#200\">Nzoom\\Export\\Streamer\\FileStreamer::setStreamingOptimizationHeaders<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#225\">Nzoom\\Export\\Streamer\\FileStreamer::outputChunk<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#243\">Nzoom\\Export\\Streamer\\FileStreamer::isClientConnected<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#253\">Nzoom\\Export\\Streamer\\FileStreamer::increaseExecutionTime<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#265\">Nzoom\\Export\\Streamer\\FileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#278\">Nzoom\\Export\\Streamer\\FileStreamer::getFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#288\">Nzoom\\Export\\Streamer\\FileStreamer::getMimeType<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#56\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::initializeGenerator<\/a>"],[0,8,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#68\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::performStreaming<\/a>"],[0,4,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#98\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::cleanup<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#123\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::setTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#140\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#150\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::reset<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\PointerFileStreamer::__construct<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\PointerFileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#93\">Nzoom\\Export\\Streamer\\PointerFileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#110\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getChunkSize<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#121\">Nzoom\\Export\\Streamer\\PointerFileStreamer::setChunkSize<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#136\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getTotalSize<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#25\">Nzoom\\Export\\Streamer\\StreamHeaders::addHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#36\">Nzoom\\Export\\Streamer\\StreamHeaders::setHeaders<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#47\">Nzoom\\Export\\Streamer\\StreamHeaders::getHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#57\">Nzoom\\Export\\Streamer\\StreamHeaders::getAll<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#67\">Nzoom\\Export\\Streamer\\StreamHeaders::clear<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#78\">Nzoom\\Export\\Streamer\\StreamHeaders::hasHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#89\">Nzoom\\Export\\Streamer\\StreamHeaders::removeHeader<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#101\">Nzoom\\Export\\Streamer\\StreamHeaders::setFileContentHeaders<\/a>"],[100,2,"<a href=\"Streamer\/StreamHeaders.php.html#117\">Nzoom\\Export\\Streamer\\StreamHeaders::sanitizeFilename<\/a>"],[100,4,"<a href=\"Streamer\/StreamHeaders.php.html#138\">Nzoom\\Export\\Streamer\\StreamHeaders::prepareCacheHeaders<\/a>"],[40,3,"<a href=\"Streamer\/StreamHeaders.php.html#168\">Nzoom\\Export\\Streamer\\StreamHeaders::sendPreparedHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#189\">Nzoom\\Export\\Streamer\\StreamHeaders::flushHeaders<\/a>"],[100,1,"<a href=\"Streamer\/StreamHeaders.php.html#204\">Nzoom\\Export\\Streamer\\StreamHeaders::send304NotModified<\/a>"],[100,7,"<a href=\"Streamer\/StreamHeaders.php.html#223\">Nzoom\\Export\\Streamer\\StreamHeaders::handleCacheValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
