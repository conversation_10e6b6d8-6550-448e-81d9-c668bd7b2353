<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item active">/var/www/Nzoom-Hella/_libs/Nzoom/Export</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="88.99" aria-valuemin="0" aria-valuemax="100" style="width: 88.99%">
           <span class="sr-only">88.99% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">88.99%</div></td>
       <td class="success small"><div align="right">1552&nbsp;/&nbsp;1744</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="72.62" aria-valuemin="0" aria-valuemax="100" style="width: 72.62%">
           <span class="sr-only">72.62% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">72.62%</div></td>
       <td class="success small"><div align="right">191&nbsp;/&nbsp;263</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="17.65" aria-valuemin="0" aria-valuemax="100" style="width: 17.65%">
           <span class="sr-only">17.65% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">17.65%</div></td>
       <td class="danger small"><div align="right">3&nbsp;/&nbsp;17</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Adapter/index.html">Adapter</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="88.94" aria-valuemin="0" aria-valuemax="100" style="width: 88.94%">
           <span class="sr-only">88.94% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">88.94%</div></td>
       <td class="success small"><div align="right">764&nbsp;/&nbsp;859</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="63.29" aria-valuemin="0" aria-valuemax="100" style="width: 63.29%">
           <span class="sr-only">63.29% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">63.29%</div></td>
       <td class="warning small"><div align="right">50&nbsp;/&nbsp;79</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Entity/index.html">Entity</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="96.13" aria-valuemin="0" aria-valuemax="100" style="width: 96.13%">
           <span class="sr-only">96.13% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">96.13%</div></td>
       <td class="success small"><div align="right">298&nbsp;/&nbsp;310</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.55" aria-valuemin="0" aria-valuemax="100" style="width: 92.55%">
           <span class="sr-only">92.55% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.55%</div></td>
       <td class="success small"><div align="right">87&nbsp;/&nbsp;94</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="40.00" aria-valuemin="0" aria-valuemax="100" style="width: 40.00%">
           <span class="sr-only">40.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">40.00%</div></td>
       <td class="warning small"><div align="right">2&nbsp;/&nbsp;5</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-directory.svg" class="octicon" /><a href="Factory/index.html">Factory</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.18" aria-valuemin="0" aria-valuemax="100" style="width: 98.18%">
           <span class="sr-only">98.18% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.18%</div></td>
       <td class="success small"><div align="right">54&nbsp;/&nbsp;55</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="88.89" aria-valuemin="0" aria-valuemax="100" style="width: 88.89%">
           <span class="sr-only">88.89% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">88.89%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;9</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="_icons/file-directory.svg" class="octicon" /><a href="Streamer/index.html">Streamer</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="48.34" aria-valuemin="0" aria-valuemax="100" style="width: 48.34%">
           <span class="sr-only">48.34% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">48.34%</div></td>
       <td class="warning small"><div align="right">73&nbsp;/&nbsp;151</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="27.91" aria-valuemin="0" aria-valuemax="100" style="width: 27.91%">
           <span class="sr-only">27.91% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">27.91%</div></td>
       <td class="danger small"><div align="right">12&nbsp;/&nbsp;43</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="DataFactory.php.html">DataFactory.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="99.02" aria-valuemin="0" aria-valuemax="100" style="width: 99.02%">
           <span class="sr-only">99.02% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">99.02%</div></td>
       <td class="success small"><div align="right">101&nbsp;/&nbsp;102</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="88.89" aria-valuemin="0" aria-valuemax="100" style="width: 88.89%">
           <span class="sr-only">88.89% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">88.89%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;9</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="ExportActionFactory.php.html">ExportActionFactory.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">193&nbsp;/&nbsp;193</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">13&nbsp;/&nbsp;13</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="_icons/file-code.svg" class="octicon" /><a href="ExportService.php.html">ExportService.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="93.24" aria-valuemin="0" aria-valuemax="100" style="width: 93.24%">
           <span class="sr-only">93.24% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">93.24%</div></td>
       <td class="success small"><div align="right">69&nbsp;/&nbsp;74</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="81.25" aria-valuemin="0" aria-valuemax="100" style="width: 81.25%">
           <span class="sr-only">81.25% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">81.25%</div></td>
       <td class="success small"><div align="right">13&nbsp;/&nbsp;16</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 16 6:37:19 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
