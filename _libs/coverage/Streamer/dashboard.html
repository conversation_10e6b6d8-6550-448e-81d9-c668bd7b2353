<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Streamer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Streamer</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">26%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">321</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">220</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FileStreamer.php.html#85"><abbr title="Nzoom\Export\Streamer\FileStreamer::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileStreamer.php.html#96"><abbr title="Nzoom\Export\Streamer\FileStreamer::setTimeIncrement">setTimeIncrement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#204"><abbr title="Nzoom\Export\Streamer\StreamHeaders::send304NotModified">send304NotModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#89"><abbr title="Nzoom\Export\Streamer\StreamHeaders::removeHeader">removeHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#78"><abbr title="Nzoom\Export\Streamer\StreamHeaders::hasHeader">hasHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#67"><abbr title="Nzoom\Export\Streamer\StreamHeaders::clear">clear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#57"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getAll">getAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#47"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#36"><abbr title="Nzoom\Export\Streamer\StreamHeaders::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PointerFileStreamer.php.html#136"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PointerFileStreamer.php.html#110"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getChunkSize">getChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#150"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#140"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileStreamer.php.html#288"><abbr title="Nzoom\Export\Streamer\FileStreamer::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileStreamer.php.html#278"><abbr title="Nzoom\Export\Streamer\FileStreamer::getFilename">getFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileStreamer.php.html#107"><abbr title="Nzoom\Export\Streamer\FileStreamer::setCacheExpires">setCacheExpires</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">27%</td></tr>
       <tr><td><a href="StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">40%</td></tr>
       <tr><td><a href="FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">25</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 13 16:14:04 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,0,0,2,0,0,0,0,2,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,1,0,1,0,2,2,2,1,13], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[78.94736842105263,23,"<a href=\"FileStreamer.php.html#12\">Nzoom\\Export\\Streamer\\FileStreamer<\/a>"],[20.588235294117645,20,"<a href=\"GeneratorFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer<\/a>"],[72.72727272727273,18,"<a href=\"PointerFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\PointerFileStreamer<\/a>"],[26.08695652173913,27,"<a href=\"StreamHeaders.php.html#11\">Nzoom\\Export\\Streamer\\StreamHeaders<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"FileStreamer.php.html#60\">Nzoom\\Export\\Streamer\\FileStreamer::__construct<\/a>"],[100,2,"<a href=\"FileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\FileStreamer::getHeaders<\/a>"],[0,1,"<a href=\"FileStreamer.php.html#85\">Nzoom\\Export\\Streamer\\FileStreamer::setHeaders<\/a>"],[0,1,"<a href=\"FileStreamer.php.html#96\">Nzoom\\Export\\Streamer\\FileStreamer::setTimeIncrement<\/a>"],[0,1,"<a href=\"FileStreamer.php.html#107\">Nzoom\\Export\\Streamer\\FileStreamer::setCacheExpires<\/a>"],[100,1,"<a href=\"FileStreamer.php.html#118\">Nzoom\\Export\\Streamer\\FileStreamer::setETag<\/a>"],[100,1,"<a href=\"FileStreamer.php.html#129\">Nzoom\\Export\\Streamer\\FileStreamer::setLastModified<\/a>"],[85.71428571428571,2,"<a href=\"FileStreamer.php.html#142\">Nzoom\\Export\\Streamer\\FileStreamer::stream<\/a>"],[100,0,"<a href=\"FileStreamer.php.html#168\">Nzoom\\Export\\Streamer\\FileStreamer::performStreaming<\/a>"],[100,2,"<a href=\"FileStreamer.php.html#175\">Nzoom\\Export\\Streamer\\FileStreamer::prepareForStreaming<\/a>"],[60,2,"<a href=\"FileStreamer.php.html#200\">Nzoom\\Export\\Streamer\\FileStreamer::setStreamingOptimizationHeaders<\/a>"],[100,2,"<a href=\"FileStreamer.php.html#225\">Nzoom\\Export\\Streamer\\FileStreamer::outputChunk<\/a>"],[100,1,"<a href=\"FileStreamer.php.html#243\">Nzoom\\Export\\Streamer\\FileStreamer::isClientConnected<\/a>"],[100,2,"<a href=\"FileStreamer.php.html#253\">Nzoom\\Export\\Streamer\\FileStreamer::increaseExecutionTime<\/a>"],[100,2,"<a href=\"FileStreamer.php.html#265\">Nzoom\\Export\\Streamer\\FileStreamer::cleanup<\/a>"],[0,1,"<a href=\"FileStreamer.php.html#278\">Nzoom\\Export\\Streamer\\FileStreamer::getFilename<\/a>"],[0,1,"<a href=\"FileStreamer.php.html#288\">Nzoom\\Export\\Streamer\\FileStreamer::getMimeType<\/a>"],[83.33333333333334,2,"<a href=\"GeneratorFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::__construct<\/a>"],[66.66666666666666,2,"<a href=\"GeneratorFileStreamer.php.html#56\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::initializeGenerator<\/a>"],[0,8,"<a href=\"GeneratorFileStreamer.php.html#68\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::performStreaming<\/a>"],[0,4,"<a href=\"GeneratorFileStreamer.php.html#98\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::cleanup<\/a>"],[0,2,"<a href=\"GeneratorFileStreamer.php.html#123\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::setTotalSize<\/a>"],[0,1,"<a href=\"GeneratorFileStreamer.php.html#140\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"GeneratorFileStreamer.php.html#150\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::reset<\/a>"],[93.33333333333333,6,"<a href=\"PointerFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\PointerFileStreamer::__construct<\/a>"],[75,6,"<a href=\"PointerFileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\PointerFileStreamer::performStreaming<\/a>"],[100,2,"<a href=\"PointerFileStreamer.php.html#93\">Nzoom\\Export\\Streamer\\PointerFileStreamer::cleanup<\/a>"],[0,1,"<a href=\"PointerFileStreamer.php.html#110\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getChunkSize<\/a>"],[0,2,"<a href=\"PointerFileStreamer.php.html#121\">Nzoom\\Export\\Streamer\\PointerFileStreamer::setChunkSize<\/a>"],[0,1,"<a href=\"PointerFileStreamer.php.html#136\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getTotalSize<\/a>"],[100,1,"<a href=\"StreamHeaders.php.html#25\">Nzoom\\Export\\Streamer\\StreamHeaders::addHeader<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#36\">Nzoom\\Export\\Streamer\\StreamHeaders::setHeaders<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#47\">Nzoom\\Export\\Streamer\\StreamHeaders::getHeader<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#57\">Nzoom\\Export\\Streamer\\StreamHeaders::getAll<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#67\">Nzoom\\Export\\Streamer\\StreamHeaders::clear<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#78\">Nzoom\\Export\\Streamer\\StreamHeaders::hasHeader<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#89\">Nzoom\\Export\\Streamer\\StreamHeaders::removeHeader<\/a>"],[100,1,"<a href=\"StreamHeaders.php.html#101\">Nzoom\\Export\\Streamer\\StreamHeaders::setFileContentHeaders<\/a>"],[75,2,"<a href=\"StreamHeaders.php.html#117\">Nzoom\\Export\\Streamer\\StreamHeaders::sanitizeFilename<\/a>"],[0,4,"<a href=\"StreamHeaders.php.html#138\">Nzoom\\Export\\Streamer\\StreamHeaders::prepareCacheHeaders<\/a>"],[40,3,"<a href=\"StreamHeaders.php.html#168\">Nzoom\\Export\\Streamer\\StreamHeaders::sendPreparedHeaders<\/a>"],[0,2,"<a href=\"StreamHeaders.php.html#189\">Nzoom\\Export\\Streamer\\StreamHeaders::flushHeaders<\/a>"],[0,1,"<a href=\"StreamHeaders.php.html#204\">Nzoom\\Export\\Streamer\\StreamHeaders::send304NotModified<\/a>"],[27.27272727272727,7,"<a href=\"StreamHeaders.php.html#223\">Nzoom\\Export\\Streamer\\StreamHeaders::handleCacheValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
