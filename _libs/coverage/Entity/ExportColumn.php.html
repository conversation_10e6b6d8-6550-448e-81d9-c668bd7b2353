<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Entity/ExportColumn.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Entity</a></li>
         <li class="breadcrumb-item active">ExportColumn.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;30</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;16</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="Nzoom\Export\Entity\ExportColumn">ExportColumn</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;30</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;16</div></td>
       <td class="success small">18</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#53"><abbr title="__construct(string $varName, string $label, string $type, string $format, ?int $width, array $styles)">__construct</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">6&nbsp;/&nbsp;6</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#74"><abbr title="getVarName(): string">getVarName</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#86"><abbr title="setVarName(string $varName)">setVarName</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#100"><abbr title="getLabel(): string">getLabel</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#110"><abbr title="setLabel(string $label)">setLabel</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#120"><abbr title="getType(): string">getType</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#131"><abbr title="setType(string $type)">setType</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">7&nbsp;/&nbsp;7</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#150"><abbr title="getFormat(): string">getFormat</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#161"><abbr title="setFormat(string $format)">setFormat</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#171"><abbr title="getWidth(): ?int">getWidth</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#181"><abbr title="setWidth(?int $width)">setWidth</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#191"><abbr title="getStyles(): array">getStyles</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#201"><abbr title="setStyles(array $styles)">setStyles</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#212"><abbr title="addStyle(string $name, $value)">addStyle</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#223"><abbr title="validateValue($value): bool">validateValue</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#235"><abbr title="createValue($value): Nzoom\Export\Entity\ExportValue">createValue</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Nzoom</span><span class="default">\</span><span class="default">Export</span><span class="default">\</span><span class="default">Entity</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Class&nbsp;ExportColumn</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Represents&nbsp;a&nbsp;column&nbsp;in&nbsp;an&nbsp;export&nbsp;header</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">ExportColumn</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string&nbsp;The&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$varName</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string&nbsp;The&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$label</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string&nbsp;The&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;string&nbsp;The&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$format</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;int|null&nbsp;The&nbsp;width&nbsp;of&nbsp;the&nbsp;column&nbsp;(for&nbsp;visual&nbsp;formatting)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$width</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;array&nbsp;Additional&nbsp;styling&nbsp;properties</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="default">$styles</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;ExportColumn&nbsp;constructor</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$varName&nbsp;The&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$label&nbsp;The&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$type&nbsp;The&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$format&nbsp;The&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$width&nbsp;The&nbsp;width&nbsp;of&nbsp;the&nbsp;column</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$styles&nbsp;Additional&nbsp;styling&nbsp;properties</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\InvalidArgumentException&nbsp;If&nbsp;the&nbsp;type&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$varName</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$label</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">ExportValue</span><span class="default">::</span><span class="default">TYPE_STRING</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$format</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$width</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$styles</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="147 tests cover line 61" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithEmptyVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setVarName</span><span class="keyword">(</span><span class="default">$varName</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="146 tests cover line 62" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setLabel</span><span class="keyword">(</span><span class="default">$label</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="146 tests cover line 63" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setType</span><span class="keyword">(</span><span class="default">$type</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 64" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setFormat</span><span class="keyword">(</span><span class="default">$format</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 65" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setWidth</span><span class="keyword">(</span><span class="default">$width</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 66" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">setStyles</span><span class="keyword">(</span><span class="default">$styles</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getVarName</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="121 tests cover line 76" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">varName</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$varName&nbsp;The&nbsp;variable&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;$this</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\InvalidArgumentException&nbsp;If&nbsp;the&nbsp;variable&nbsp;name&nbsp;is&nbsp;empty</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setVarName</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$varName</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="147 tests cover line 88" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithEmptyVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$varName</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 89" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithEmptyVarName&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">InvalidArgumentException</span><span class="keyword">(</span><span class="default">'Variable&nbsp;name&nbsp;cannot&nbsp;be&nbsp;empty'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="146 tests cover line 92" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">varName</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$varName</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getLabel</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="30 tests cover line 102" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">label</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$label&nbsp;The&nbsp;display&nbsp;label</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setLabel</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$label</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="146 tests cover line 112" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">label</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$label</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getType</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="22 tests cover line 122" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$type&nbsp;The&nbsp;data&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;\InvalidArgumentException&nbsp;If&nbsp;the&nbsp;type&nbsp;is&nbsp;invalid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setType</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Validate&nbsp;the&nbsp;type&nbsp;against&nbsp;the&nbsp;valid&nbsp;types&nbsp;in&nbsp;ExportValue</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="146 tests cover line 134" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ExportValue</span><span class="default">::</span><span class="default">getValidTypes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 135" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\</span><span class="default">InvalidArgumentException</span><span class="keyword">(</span><span class="default">sprintf</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 136" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'Invalid&nbsp;type&nbsp;&quot;%s&quot;.&nbsp;Valid&nbsp;types&nbsp;are:&nbsp;%s'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 137" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$type</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 138" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">implode</span><span class="keyword">(</span><span class="default">',&nbsp;'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ExportValue</span><span class="default">::</span><span class="default">getValidTypes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 139" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithInvalidType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 142" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getFormat</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="14 tests cover line 152" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">format</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$format&nbsp;The&nbsp;format&nbsp;specification</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;$this</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setFormat</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$format</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 163" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">format</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$format</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;width&nbsp;of&nbsp;the&nbsp;column</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;int|null&nbsp;The&nbsp;width</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getWidth</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">int</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 173" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">width</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;width&nbsp;of&nbsp;the&nbsp;column</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;int|null&nbsp;$width&nbsp;The&nbsp;width</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setWidth</span><span class="keyword">(</span><span class="keyword">?</span><span class="default">int</span><span class="default">&nbsp;</span><span class="default">$width</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 183" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">width</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$width</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;styles</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&nbsp;The&nbsp;styles</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getStyles</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 193" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">styles</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Set&nbsp;the&nbsp;styles</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;array&nbsp;$styles&nbsp;The&nbsp;styles</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setStyles</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$styles</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="145 tests cover line 203" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithTabDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithPipeDelimiter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithBom&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportLogsProgress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\CsvExportFormatAdapterTest::testExportWithCustomEnclosure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithSpecialCharacters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testProcessExportData&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithCustomOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportXlsFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testCreateSpreadsheet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithLargeDataset&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testHandleSpreadsheetErrorIntegrationWithExport&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\ExcelExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMixedDataTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidJson&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportLogsCompletion&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithJsonOptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToTempFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportToPhpOutput&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithObjectStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNestedStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnwritableFile&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithInvalidFilePointer&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Adapter\JsonExportFormatAdapterTest::testExportWithUnsupportedType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testHeaderCreationWithDefaultValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreaming&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithOutlookFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testFieldTypeMapping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCursorStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsPreserveFilterStructure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testCreateStreamingWithDefaultPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingPaginationBehavior&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtInvalidIndex&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithoutHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testMetadataOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetHeader&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testLazyLoadingPreventDirectRecordOperations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSetPageSize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIterator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCreateRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddMultipleRecords&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testConstructorWithMetadata&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testIsEmptyWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnInvalidColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testSortByColumnWithNullValues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testToArrayFormatted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordsWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testGetRecordAtWithLazyLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testCountWithEagerLoading&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testComplexExportScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportWithInvalidFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\ExportServiceTest::testExportSuccess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithMinimalParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAllValidTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testConstructorWithAllParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetStyles&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidthWithNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetWidth&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetFormat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetTypeWithInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabelWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetLabel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarNameWithEmptyString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testSetVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetVarNames&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testCountableImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testIteratorImplementation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidCount&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithInvalidVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsWithEmptyArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumnsPartialOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testReorderColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetLabels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnByVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumnAt&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testGetColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testHasColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumnWithDuplicateVarName&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddMultipleColumns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testAddColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithInvalidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">styles</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$styles</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Add&nbsp;a&nbsp;style</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;string&nbsp;$name&nbsp;The&nbsp;style&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;mixed&nbsp;$value&nbsp;The&nbsp;style&nbsp;value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">addStyle</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$name</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 214" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyleOverwritesExisting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testAddStyle&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testComplexStyling&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">styles</span><span class="keyword">[</span><span class="default">$name</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;a&nbsp;value&nbsp;against&nbsp;this&nbsp;column's&nbsp;type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;mixed&nbsp;$value&nbsp;The&nbsp;value&nbsp;to&nbsp;validate</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;bool&nbsp;True&nbsp;if&nbsp;valid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateValue</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="20 tests cover line 225" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$exportValue</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ExportValue</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">format</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="20 tests cover line 226" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testModelWithoutGetExportVarTypeMethod&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithLargeModelSet&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testInvokeWithModels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\DataFactoryTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testValidate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportDataTest::testAddRecordWithValidation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidBoolean&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidFloat&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithInvalidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidInteger&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testValidateValueWithValidString&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordInvalidType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testValidateRecordValid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportHeaderTest::testComplexScenario&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testValidateWithValidRecord&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportRecordTest::testComplexScenario&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$exportValue</span><span class="default">-&gt;</span><span class="default">validate</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;an&nbsp;ExportValue&nbsp;for&nbsp;this&nbsp;column</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;mixed&nbsp;$value&nbsp;The&nbsp;value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;ExportValue&nbsp;The&nbsp;created&nbsp;ExportValue</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">createValue</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">ExportValue</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 237" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValueWithDifferentTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Nzoom\Export\Entity\ExportColumnTest::testCreateValue&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ExportValue</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">type</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">format</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Mon Jun 16 7:22:07 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/file.js?v=9.2.32" type="text/javascript"></script>
 </body>
</html>
