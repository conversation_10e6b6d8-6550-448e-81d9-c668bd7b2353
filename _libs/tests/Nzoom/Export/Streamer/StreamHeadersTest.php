<?php

namespace Tests\Nzoom\Export\Streamer;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Streamer\StreamHeaders;

/**
 * Test case for StreamHeaders
 *
 * Tests HTTP header management functionality for streaming operations
 */
class StreamHeadersTest extends ExportTestCase
{
    private StreamHeaders $streamHeaders;

    protected function setUp(): void
    {
        parent::setUp();
        $this->streamHeaders = new StreamHeaders();
    }

    // Test basic header management

    public function testAddHeader(): void
    {
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        
        $this->assertEquals('application/json', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertTrue($this->streamHeaders->hasHeader('Content-Type'));
    }

    public function testAddMultipleHeaders(): void
    {
        $this->streamHeaders->addHeader('Content-Type', 'text/csv');
        $this->streamHeaders->addHeader('Content-Length', '1024');
        $this->streamHeaders->addHeader('Cache-Control', 'no-cache');
        
        $this->assertEquals('text/csv', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('1024', $this->streamHeaders->getHeader('Content-Length'));
        $this->assertEquals('no-cache', $this->streamHeaders->getHeader('Cache-Control'));
    }

    public function testAddHeaderOverwritesExisting(): void
    {
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('Content-Type', 'text/csv');
        
        $this->assertEquals('text/csv', $this->streamHeaders->getHeader('Content-Type'));
    }

    public function testSetHeaders(): void
    {
        // Add some initial headers
        $this->streamHeaders->addHeader('Initial-Header', 'value');
        
        // Set new headers (should replace all existing)
        $headers = [
            'Content-Type' => 'application/pdf',
            'Content-Length' => '2048',
            'Cache-Control' => 'public'
        ];
        
        $this->streamHeaders->setHeaders($headers);
        
        $this->assertEquals($headers, $this->streamHeaders->getAll());
        $this->assertFalse($this->streamHeaders->hasHeader('Initial-Header'));
    }

    public function testGetHeader(): void
    {
        $this->streamHeaders->addHeader('Test-Header', 'test-value');
        
        $this->assertEquals('test-value', $this->streamHeaders->getHeader('Test-Header'));
        $this->assertNull($this->streamHeaders->getHeader('Non-Existent-Header'));
    }

    public function testGetAll(): void
    {
        $headers = [
            'Content-Type' => 'application/xlsx',
            'Content-Disposition' => 'attachment; filename="test.xlsx"',
            'Cache-Control' => 'no-store'
        ];
        
        foreach ($headers as $name => $value) {
            $this->streamHeaders->addHeader($name, $value);
        }
        
        $this->assertEquals($headers, $this->streamHeaders->getAll());
    }

    public function testGetAllEmpty(): void
    {
        $this->assertEquals([], $this->streamHeaders->getAll());
    }

    public function testHasHeader(): void
    {
        $this->assertFalse($this->streamHeaders->hasHeader('Content-Type'));
        
        $this->streamHeaders->addHeader('Content-Type', 'text/plain');
        
        $this->assertTrue($this->streamHeaders->hasHeader('Content-Type'));
        $this->assertFalse($this->streamHeaders->hasHeader('Non-Existent'));
    }

    public function testRemoveHeader(): void
    {
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('Cache-Control', 'no-cache');
        
        $this->assertTrue($this->streamHeaders->hasHeader('Content-Type'));
        
        $this->streamHeaders->removeHeader('Content-Type');
        
        $this->assertFalse($this->streamHeaders->hasHeader('Content-Type'));
        $this->assertTrue($this->streamHeaders->hasHeader('Cache-Control'));
    }

    public function testRemoveNonExistentHeader(): void
    {
        // Should not throw an error
        $this->streamHeaders->removeHeader('Non-Existent-Header');
        $this->assertEquals([], $this->streamHeaders->getAll());
    }

    public function testClear(): void
    {
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('Cache-Control', 'no-cache');
        
        $this->assertCount(2, $this->streamHeaders->getAll());
        
        $this->streamHeaders->clear();
        
        $this->assertEquals([], $this->streamHeaders->getAll());
        $this->assertFalse($this->streamHeaders->hasHeader('Content-Type'));
        $this->assertFalse($this->streamHeaders->hasHeader('Cache-Control'));
    }

    // Test file content headers

    public function testSetFileContentHeaders(): void
    {
        $this->streamHeaders->setFileContentHeaders('application/vnd.ms-excel', 'report.xlsx');
        
        $this->assertEquals('application/vnd.ms-excel', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('attachment; filename="report.xlsx"', $this->streamHeaders->getHeader('Content-Disposition'));
    }

    public function testSetFileContentHeadersWithSpecialCharacters(): void
    {
        $this->streamHeaders->setFileContentHeaders('text/csv', 'my report (2024).csv');
        
        $this->assertEquals('text/csv', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('attachment; filename="my_report__2024_.csv"', $this->streamHeaders->getHeader('Content-Disposition'));
    }

    public function testSetFileContentHeadersWithEmptyFilename(): void
    {
        $this->streamHeaders->setFileContentHeaders('application/json', '');
        
        $this->assertEquals('application/json', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('attachment; filename="download"', $this->streamHeaders->getHeader('Content-Disposition'));
    }

    public function testSetFileContentHeadersWithInvalidCharacters(): void
    {
        $this->streamHeaders->setFileContentHeaders('application/pdf', 'file/with\\invalid:chars*.pdf');
        
        $this->assertEquals('application/pdf', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('attachment; filename="file_with_invalid_chars_.pdf"', $this->streamHeaders->getHeader('Content-Disposition'));
    }

    // Test cache headers

    public function testPrepareCacheHeadersNoCache(): void
    {
        $this->streamHeaders->prepareCacheHeaders(0, null, null);
        
        $this->assertEquals('no-cache, no-store, must-revalidate', $this->streamHeaders->getHeader('Cache-Control'));
        $this->assertEquals('no-cache', $this->streamHeaders->getHeader('Pragma'));
        $this->assertEquals('0', $this->streamHeaders->getHeader('Expires'));
    }

    public function testPrepareCacheHeadersWithCache(): void
    {
        $cacheExpires = 3600; // 1 hour
        $this->streamHeaders->prepareCacheHeaders($cacheExpires, null, null);
        
        $this->assertEquals('public, max-age=3600', $this->streamHeaders->getHeader('Cache-Control'));
        
        // Check that Expires header is set to approximately 1 hour from now
        $expiresHeader = $this->streamHeaders->getHeader('Expires');
        $this->assertNotNull($expiresHeader);
        $this->assertStringContainsString('GMT', $expiresHeader);
    }

    public function testPrepareCacheHeadersWithETag(): void
    {
        $etag = 'abc123def456';
        $this->streamHeaders->prepareCacheHeaders(3600, $etag, null);
        
        $this->assertEquals('"abc123def456"', $this->streamHeaders->getHeader('ETag'));
    }

    public function testPrepareCacheHeadersWithLastModified(): void
    {
        $lastModified = strtotime('2024-01-15 14:30:00');
        $this->streamHeaders->prepareCacheHeaders(3600, null, $lastModified);
        
        $lastModifiedHeader = $this->streamHeaders->getHeader('Last-Modified');
        $this->assertNotNull($lastModifiedHeader);
        $this->assertStringContainsString('GMT', $lastModifiedHeader);
        $this->assertStringContainsString('15 Jan 2024', $lastModifiedHeader);
    }

    public function testPrepareCacheHeadersWithAllOptions(): void
    {
        $cacheExpires = 7200; // 2 hours
        $etag = 'full-test-etag';
        $lastModified = strtotime('2024-01-15 14:30:00');
        
        $this->streamHeaders->prepareCacheHeaders($cacheExpires, $etag, $lastModified);
        
        $this->assertEquals('public, max-age=7200', $this->streamHeaders->getHeader('Cache-Control'));
        $this->assertEquals('"full-test-etag"', $this->streamHeaders->getHeader('ETag'));
        $this->assertStringContainsString('15 Jan 2024', $this->streamHeaders->getHeader('Last-Modified'));
        $this->assertStringContainsString('GMT', $this->streamHeaders->getHeader('Expires'));
    }

    // Test cache validation methods

    public function testHandleCacheValidationWithETagMatch(): void
    {
        $etag = 'test-etag-123';
        $lastModified = time() - 3600;

        // Simulate client sending If-None-Match header
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"test-etag-123"';

        // Capture output to check for 304 response
        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
    }

    public function testHandleCacheValidationWithETagMismatch(): void
    {
        $etag = 'test-etag-123';
        $lastModified = time() - 3600;

        // Simulate client sending different If-None-Match header
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"different-etag"';

        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);

        $this->assertFalse($result);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
    }

    public function testHandleCacheValidationWithLastModifiedMatch(): void
    {
        $etag = null;
        $lastModified = strtotime('2024-01-15 14:30:00');

        // Simulate client sending If-Modified-Since header with same or later time
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 14:30:00 GMT';

        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testHandleCacheValidationWithLastModifiedNewer(): void
    {
        $etag = null;
        $lastModified = strtotime('2024-01-15 14:30:00');

        // Simulate client sending If-Modified-Since header with earlier time
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 13:30:00 GMT';

        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);

        $this->assertFalse($result);

        // Clean up
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testHandleCacheValidationWithNoClientHeaders(): void
    {
        $etag = 'test-etag';
        $lastModified = time() - 3600;

        // Ensure no client cache headers are set
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);

        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);

        $this->assertFalse($result);
    }

    public function testHandleCacheValidationWithNullParameters(): void
    {
        // Ensure no client cache headers are set
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);

        $result = $this->streamHeaders->handleCacheValidation(3600, null, null);

        $this->assertFalse($result);
    }

    // Test 304 Not Modified response

    public function testSend304NotModified(): void
    {
        $etag = 'test-304-etag';
        $lastModified = strtotime('2024-01-15 14:30:00');

        // This test is tricky because we can't easily test http_response_code() and header() functions
        // in unit tests without output buffering and mocking. We'll test that the method runs without error
        // and that the appropriate headers are prepared.

        ob_start();
        $this->streamHeaders->send304NotModified(3600, $etag, $lastModified);
        $output = ob_get_clean();

        // Verify that cache headers were prepared
        $this->assertEquals('public, max-age=3600', $this->streamHeaders->getHeader('Cache-Control'));
        $this->assertEquals('"test-304-etag"', $this->streamHeaders->getHeader('ETag'));
        $this->assertStringContainsString('15 Jan 2024', $this->streamHeaders->getHeader('Last-Modified'));
    }

    // Test header sending (limited testing due to PHP's header() function)

    public function testSendPreparedHeadersWhenHeadersAlreadySent(): void
    {
        // This is difficult to test properly in unit tests since headers_sent()
        // depends on actual HTTP output. We'll test the method runs without error.

        $this->streamHeaders->addHeader('Content-Type', 'application/json');

        // This should not throw an exception even if headers are "sent"
        $this->streamHeaders->sendPreparedHeaders();

        // If we get here, the method completed successfully
        $this->assertTrue(true);
    }

    // Test private method behavior through public interface

    public function testSanitizeFilenameThroughSetFileContentHeaders(): void
    {
        // Test various problematic filenames
        $testCases = [
            ['file name with spaces.txt', 'file_name_with_spaces.txt'],
            ['file/with\\slashes.pdf', 'file_with_slashes.pdf'],
            ['file:with*special?chars.csv', 'file_with_special_chars.csv'],
            ['file<with>pipes|and"quotes.xlsx', 'file_with_pipes_and_quotes.xlsx'],
            ['', 'download'], // Empty filename
            ['normal_file.txt', 'normal_file.txt'], // Normal filename should be unchanged
        ];

        foreach ($testCases as [$input, $expected]) {
            $this->streamHeaders->clear();
            $this->streamHeaders->setFileContentHeaders('application/octet-stream', $input);

            $disposition = $this->streamHeaders->getHeader('Content-Disposition');
            $this->assertStringContainsString('filename="' . $expected . '"', $disposition,
                "Failed for input: '$input'");
        }
    }

    // Test edge cases and error conditions

    public function testHeadersWithEmptyValues(): void
    {
        $this->streamHeaders->addHeader('Empty-Header', '');

        $this->assertEquals('', $this->streamHeaders->getHeader('Empty-Header'));
        $this->assertTrue($this->streamHeaders->hasHeader('Empty-Header'));
    }

    public function testHeadersWithSpecialCharacters(): void
    {
        $this->streamHeaders->addHeader('Special-Header', 'value with spaces and symbols: !@#$%');

        $this->assertEquals('value with spaces and symbols: !@#$%', $this->streamHeaders->getHeader('Special-Header'));
    }

    public function testCacheHeadersWithZeroExpiration(): void
    {
        $this->streamHeaders->prepareCacheHeaders(0, 'etag', time());

        // Should still set ETag and Last-Modified even with no-cache
        $this->assertEquals('"etag"', $this->streamHeaders->getHeader('ETag'));
        $this->assertNotNull($this->streamHeaders->getHeader('Last-Modified'));
        $this->assertEquals('no-cache, no-store, must-revalidate', $this->streamHeaders->getHeader('Cache-Control'));
    }

    public function testMultipleCallsToPrepareCacheHeaders(): void
    {
        // First call
        $this->streamHeaders->prepareCacheHeaders(3600, 'etag1', time() - 1000);
        $firstEtag = $this->streamHeaders->getHeader('ETag');

        // Second call should overwrite
        $this->streamHeaders->prepareCacheHeaders(7200, 'etag2', time() - 2000);
        $secondEtag = $this->streamHeaders->getHeader('ETag');

        $this->assertEquals('"etag1"', $firstEtag);
        $this->assertEquals('"etag2"', $secondEtag);
        $this->assertEquals('public, max-age=7200', $this->streamHeaders->getHeader('Cache-Control'));
    }

    // Test additional edge cases for better coverage

    public function testHandleCacheValidationWithInvalidIfModifiedSince(): void
    {
        $etag = null;
        $lastModified = time() - 3600;

        // Simulate client sending invalid If-Modified-Since header
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'invalid-date-string';

        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);

        // Should return false because strtotime() will return false for invalid date
        $this->assertFalse($result);

        // Clean up
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testHandleCacheValidationWithETagButNoLastModified(): void
    {
        $etag = 'test-etag';
        $lastModified = null;

        // Simulate client sending If-None-Match header
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"test-etag"';

        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
    }

    public function testHandleCacheValidationWithLastModifiedButNoETag(): void
    {
        $etag = null;
        $lastModified = strtotime('2024-01-15 14:30:00');

        // Simulate client sending If-Modified-Since header with same time
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 14:30:00 GMT';

        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testSendPreparedHeadersWithOutputBuffering(): void
    {
        // Start output buffering to simulate a scenario where ob_get_level() > 0
        ob_start();

        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('Cache-Control', 'no-cache');

        // This should not throw an exception
        $this->streamHeaders->sendPreparedHeaders();

        // Clean up output buffer
        ob_end_clean();

        // If we get here, the method completed successfully
        $this->assertTrue(true);
    }

    public function testSendPreparedHeadersWithNoHeaders(): void
    {
        // Test sending headers when no headers are set
        $this->streamHeaders->sendPreparedHeaders();

        // Should complete without error
        $this->assertEquals([], $this->streamHeaders->getAll());
    }

    public function testPrepareCacheHeadersWithNegativeExpiration(): void
    {
        // Test with negative expiration (the class allows negative values)
        $this->streamHeaders->prepareCacheHeaders(-1, null, null);

        // The class doesn't treat negative values specially, it just uses them
        $this->assertEquals('public, max-age=-1', $this->streamHeaders->getHeader('Cache-Control'));

        // Expires header should be set to a time in the past
        $expiresHeader = $this->streamHeaders->getHeader('Expires');
        $this->assertNotNull($expiresHeader);
        $this->assertStringContainsString('GMT', $expiresHeader);
    }

    public function testFileContentHeadersWithUnicodeFilename(): void
    {
        $this->streamHeaders->setFileContentHeaders('text/plain', 'файл.txt');

        $this->assertEquals('text/plain', $this->streamHeaders->getHeader('Content-Type'));

        // Unicode characters should be replaced with underscores
        $disposition = $this->streamHeaders->getHeader('Content-Disposition');
        $this->assertStringContainsString('filename="', $disposition);
        $this->assertStringContainsString('.txt"', $disposition);
    }

    public function testFileContentHeadersWithOnlyExtension(): void
    {
        $this->streamHeaders->setFileContentHeaders('application/octet-stream', '.hidden');

        $disposition = $this->streamHeaders->getHeader('Content-Disposition');
        $this->assertStringContainsString('filename=".hidden"', $disposition);
    }

    public function testFileContentHeadersWithVeryLongFilename(): void
    {
        $longFilename = str_repeat('a', 200) . '.txt';
        $this->streamHeaders->setFileContentHeaders('text/plain', $longFilename);

        $disposition = $this->streamHeaders->getHeader('Content-Disposition');
        $this->assertStringContainsString('filename="', $disposition);
        $this->assertStringContainsString('.txt"', $disposition);
    }

    public function testCacheValidationWithBothETagAndLastModifiedMatch(): void
    {
        $etag = 'both-test-etag';
        $lastModified = strtotime('2024-01-15 14:30:00');

        // Simulate client sending both headers that match
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"both-test-etag"';
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 14:30:00 GMT';

        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        // Should return true on ETag match (first condition checked)
        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testCacheValidationWithETagMismatchButLastModifiedMatch(): void
    {
        $etag = 'test-etag';
        $lastModified = strtotime('2024-01-15 14:30:00');

        // Simulate client sending mismatched ETag but matching Last-Modified
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"different-etag"';
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 14:30:00 GMT';

        ob_start();
        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $output = ob_get_clean();

        // Should return true because Last-Modified matches (even though ETag doesn't)
        $this->assertTrue($result);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testSend304NotModifiedWithNoCache(): void
    {
        $etag = 'no-cache-etag';
        $lastModified = time() - 3600;

        ob_start();
        $this->streamHeaders->send304NotModified(0, $etag, $lastModified);
        $output = ob_get_clean();

        // Should set no-cache headers even for 304 response
        $this->assertEquals('no-cache, no-store, must-revalidate', $this->streamHeaders->getHeader('Cache-Control'));
        $this->assertEquals('"no-cache-etag"', $this->streamHeaders->getHeader('ETag'));
    }

    public function testSendPreparedHeadersWithoutOutputBuffering(): void
    {
        // Store original output buffer level
        $originalLevel = ob_get_level();

        // Ensure no output buffering is active to test the flush() path
        while (ob_get_level() > 0) {
            ob_end_clean();
        }

        $this->streamHeaders->addHeader('Content-Type', 'text/plain');

        // This should trigger the flush() call in flushHeaders() since ob_get_level() === 0
        $this->streamHeaders->sendPreparedHeaders();

        // Restore original output buffer level
        for ($i = 0; $i < $originalLevel; $i++) {
            ob_start();
        }

        // If we get here, the method completed successfully
        $this->assertTrue(true);
    }

    public function testComplexCacheValidationScenario(): void
    {
        // Test a complex scenario with multiple conditions
        $etag = 'complex-etag';
        $lastModified = strtotime('2024-01-15 14:30:00');

        // First test: ETag mismatch, no Last-Modified header
        $_SERVER['HTTP_IF_NONE_MATCH'] = '"different-etag"';
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);

        $result1 = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $this->assertFalse($result1);

        // Second test: No ETag header, Last-Modified mismatch
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'Mon, 15 Jan 2024 13:30:00 GMT'; // Earlier time

        $result2 = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);
        $this->assertFalse($result2);

        // Clean up
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testCacheValidationWithStrtoimeFailure(): void
    {
        // Test scenario where strtotime() returns false
        $etag = null;
        $lastModified = time() - 3600;

        // Simulate client sending completely invalid date
        $_SERVER['HTTP_IF_MODIFIED_SINCE'] = 'not-a-date-at-all';

        $result = $this->streamHeaders->handleCacheValidation(3600, $etag, $lastModified);

        // Should return false because strtotime() returns false
        $this->assertFalse($result);

        // Clean up
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);
    }

    public function testHeaderCaseSensitivity(): void
    {
        // Test that header names are case-sensitive in our implementation
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('content-type', 'text/plain');

        // Should have both headers since they're different keys
        $this->assertEquals('application/json', $this->streamHeaders->getHeader('Content-Type'));
        $this->assertEquals('text/plain', $this->streamHeaders->getHeader('content-type'));
        $this->assertCount(2, $this->streamHeaders->getAll());
    }

    public function testSetHeadersWithEmptyArray(): void
    {
        // Add some headers first
        $this->streamHeaders->addHeader('Content-Type', 'application/json');
        $this->streamHeaders->addHeader('Cache-Control', 'no-cache');

        // Set empty array should clear all headers
        $this->streamHeaders->setHeaders([]);

        $this->assertEquals([], $this->streamHeaders->getAll());
        $this->assertFalse($this->streamHeaders->hasHeader('Content-Type'));
    }

    protected function tearDown(): void
    {
        // Clean up any $_SERVER variables we might have set
        unset($_SERVER['HTTP_IF_NONE_MATCH']);
        unset($_SERVER['HTTP_IF_MODIFIED_SINCE']);

        parent::tearDown();
    }
}
