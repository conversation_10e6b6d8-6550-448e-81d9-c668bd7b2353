<?php

namespace Tests\Nzoom\Export\Streamer;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Streamer\GeneratorFileStreamer;
use Nzoom\Export\Streamer\StreamHeaders;

/**
 * Test case for GeneratorFileStreamer
 *
 * Tests generator-based file streaming functionality
 */
class GeneratorFileStreamerTest extends ExportTestCase
{
    private GeneratorFileStreamer $streamer;

    protected function setUp(): void
    {
        parent::setUp();
    }

    // Test constructor and basic functionality

    public function testConstructorWithBasicGenerator(): void
    {
        $generatorFunction = function() {
            yield 'chunk1';
            yield 'chunk2';
            yield 'chunk3';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        $this->assertEquals('test.txt', $this->streamer->getFilename());
        $this->assertEquals('text/plain', $this->streamer->getMimeType());
        $this->assertNull($this->streamer->getTotalSize());
    }

    public function testConstructorWithTotalSize(): void
    {
        $generatorFunction = function() {
            yield 'test data';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain', 1024);

        $this->assertEquals(1024, $this->streamer->getTotalSize());
        
        // Should set Content-Length header
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('1024', $headers->getHeader('Content-Length'));
    }

    public function testConstructorWithDefaultMimeType(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.bin');

        $this->assertEquals('application/octet-stream', $this->streamer->getMimeType());
    }

    public function testConstructorWithInvalidGeneratorFunction(): void
    {
        $invalidFunction = function() {
            return 'not a generator'; // Returns string instead of generator
        };

        $this->expectException(\TypeError::class);

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    // Test total size management

    public function testSetTotalSize(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt');
        
        $result = $this->streamer->setTotalSize(2048);
        
        $this->assertSame($this->streamer, $result); // Should return self for fluent interface
        $this->assertEquals(2048, $this->streamer->getTotalSize());
        
        // Should update Content-Length header
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('2048', $headers->getHeader('Content-Length'));
    }

    public function testSetTotalSizeToNull(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain', 1024);
        
        $this->streamer->setTotalSize(null);
        
        $this->assertNull($this->streamer->getTotalSize());
    }

    // Test streaming functionality

    public function testPerformStreamingDirectly(): void
    {
        $chunks = [];
        $generatorFunction = function() use (&$chunks) {
            $data = ['Hello ', 'World', '!'];
            foreach ($data as $chunk) {
                $chunks[] = $chunk; // Track what was yielded
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Use reflection to test performStreaming directly
        $reflection = new \ReflectionClass($this->streamer);
        $method = $reflection->getMethod('performStreaming');
        $method->setAccessible(true);

        // Call performStreaming directly
        $method->invoke($this->streamer);

        // Verify that all chunks were processed
        $this->assertEquals(['Hello ', 'World', '!'], $chunks);
    }

    public function testStreamWithSimpleGenerator(): void
    {
        $generatorFunction = function() {
            yield 'Hello ';
            yield 'World';
            yield '!';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test that streaming completes without error
        // We can't easily capture output due to FileStreamer's buffer management
        $this->streamer->stream();

        // If we get here, streaming completed successfully
        $this->assertTrue(true);
    }

    public function testStreamWithEmptyGenerator(): void
    {
        $generatorFunction = function() {
            return;
            yield; // This will never be reached
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'empty.txt', 'text/plain');

        // Test that streaming completes without error
        $this->streamer->stream();

        // If we get here, streaming completed successfully
        $this->assertTrue(true);
    }

    public function testStreamWithNullChunks(): void
    {
        $processedChunks = [];
        $generatorFunction = function() use (&$processedChunks) {
            $chunks = ['start', null, 'middle', '', 'end'];
            foreach ($chunks as $chunk) {
                $processedChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test that streaming completes without error
        $this->streamer->stream();

        // Verify all chunks were processed (including null and empty)
        $this->assertEquals(['start', null, 'middle', '', 'end'], $processedChunks);
    }

    public function testStreamWithNonStringChunks(): void
    {
        $processedChunks = [];
        $generatorFunction = function() use (&$processedChunks) {
            $chunks = ['text', 123, 45.67, true];
            foreach ($chunks as $chunk) {
                $processedChunks[] = $chunk;
                yield $chunk;
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test that streaming completes without error
        $this->streamer->stream();

        // Verify all chunks were processed
        $this->assertEquals(['text', 123, 45.67, true], $processedChunks);
    }

    public function testStreamWithLargeGenerator(): void
    {
        $chunkCount = 0;
        $generatorFunction = function() use (&$chunkCount) {
            for ($i = 1; $i <= 100; $i++) {
                $chunkCount++;
                yield "chunk{$i}\n";
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'large.txt', 'text/plain');

        // Test that streaming completes without error
        $this->streamer->stream();

        // Verify all chunks were generated
        $this->assertEquals(100, $chunkCount);
    }

    // Test reset functionality

    public function testReset(): void
    {
        $callCount = 0;
        $generatorFunction = function() use (&$callCount) {
            $callCount++;
            yield "call{$callCount}";
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // First stream
        ob_start();
        $this->streamer->stream();
        $output1 = ob_get_clean();

        // Reset and stream again
        $result = $this->streamer->reset();
        
        $this->assertSame($this->streamer, $result); // Should return self for fluent interface

        ob_start();
        $this->streamer->stream();
        $output2 = ob_get_clean();

        // Should have called the generator function twice
        $this->assertEquals(2, $callCount);
        $this->assertEquals('call1', $output1);
        $this->assertEquals('call2', $output2);
    }

    // Test headers functionality

    public function testHeadersIntegration(): void
    {
        $generatorFunction = function() {
            yield 'test data';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');
        
        // Get headers instance
        $headers = $this->streamer->getHeaders();
        $this->assertInstanceOf(StreamHeaders::class, $headers);
        
        // Should have file content headers set during streaming
        ob_start();
        $this->streamer->stream();
        ob_get_clean();
        
        $this->assertEquals('text/plain', $headers->getHeader('Content-Type'));
        $this->assertStringContainsString('attachment; filename="test.txt"', $headers->getHeader('Content-Disposition'));
    }

    public function testCustomHeaders(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');
        
        // Set custom headers
        $customHeaders = new StreamHeaders();
        $customHeaders->addHeader('X-Custom-Header', 'custom-value');
        $this->streamer->setHeaders($customHeaders);
        
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('custom-value', $headers->getHeader('X-Custom-Header'));
    }

    // Test cache functionality

    public function testCacheHeaders(): void
    {
        $generatorFunction = function() {
            yield 'cached content';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'cached.txt', 'text/plain');
        
        // Set cache parameters
        $this->streamer->setCacheExpires(3600);
        $this->streamer->setETag('test-etag');
        $this->streamer->setLastModified(time() - 1000);
        
        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();
        
        $headers = $this->streamer->getHeaders();
        $this->assertEquals('"test-etag"', $headers->getHeader('ETag'));
        $this->assertEquals('public, max-age=3600', $headers->getHeader('Cache-Control'));
    }

    // Test time increment functionality

    public function testTimeIncrement(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Set time increment
        $this->streamer->setTimeIncrement(60);

        // This is hard to test directly since it affects set_time_limit()
        // We'll just verify the method runs without error
        ob_start();
        $this->streamer->stream();
        ob_get_clean();

        $this->assertTrue(true); // If we get here, no exception was thrown
    }

    // Test error handling

    public function testGeneratorWithException(): void
    {
        $generatorFunction = function() {
            yield 'start';
            throw new \RuntimeException('Generator error');
            yield 'never reached';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'error.txt', 'text/plain');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Generator error');

        ob_start();
        try {
            $this->streamer->stream();
        } finally {
            ob_end_clean();
        }
    }

    public function testGeneratorFunctionThrowsException(): void
    {
        $generatorFunction = function() {
            throw new \RuntimeException('Function error');
        };

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Function error');

        new GeneratorFileStreamer($generatorFunction, 'test.txt');
    }

    // Test generator function validation

    public function testGeneratorFunctionReturnsNull(): void
    {
        $invalidFunction = function() {
            return null;
        };

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Generator function must return a Generator instance');

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    public function testGeneratorFunctionReturnsArray(): void
    {
        $invalidFunction = function() {
            return ['not', 'a', 'generator'];
        };

        $this->expectException(\TypeError::class);

        new GeneratorFileStreamer($invalidFunction, 'test.txt');
    }

    // Test edge cases

    public function testStreamWithVeryLongChunks(): void
    {
        $generatorFunction = function() {
            // Generate a very long chunk
            yield str_repeat('A', 10000);
            yield str_repeat('B', 5000);
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'long.txt', 'text/plain');

        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();

        $this->assertEquals(15000, strlen($output));
        $this->assertStringStartsWith('AAAA', $output);
        $this->assertStringEndsWith('BBBB', $output);
    }

    public function testStreamWithBinaryData(): void
    {
        $generatorFunction = function() {
            // Generate some binary data
            yield pack('C*', 0, 1, 2, 3, 255);
            yield pack('n', 65535); // Big-endian 16-bit
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'binary.bin', 'application/octet-stream');

        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();

        $this->assertEquals(7, strlen($output)); // 5 + 2 bytes
        $this->assertEquals(0, ord($output[0]));
        $this->assertEquals(255, ord($output[4]));
    }

    public function testStreamWithUnicodeData(): void
    {
        $generatorFunction = function() {
            yield 'Hello ';
            yield 'Wörld '; // German umlaut
            yield '世界'; // Chinese characters
            yield ' 🌍'; // Emoji
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'unicode.txt', 'text/plain; charset=utf-8');

        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();

        $this->assertStringContainsString('Wörld', $output);
        $this->assertStringContainsString('世界', $output);
        $this->assertStringContainsString('🌍', $output);
    }

    // Test inheritance from FileStreamer

    public function testInheritsFromFileStreamer(): void
    {
        $generatorFunction = function() {
            yield 'test';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'test.txt', 'text/plain');

        // Test inherited methods
        $this->assertEquals('test.txt', $this->streamer->getFilename());
        $this->assertEquals('text/plain', $this->streamer->getMimeType());

        // Test inherited functionality
        $this->streamer->setCacheExpires(1800);
        $this->streamer->setETag('inherited-etag');
        $this->streamer->setLastModified(time() - 500);

        // These should work without error
        $this->assertTrue(true);
    }

    // Test multiple streaming calls

    public function testMultipleStreamCalls(): void
    {
        $callCount = 0;
        $generatorFunction = function() use (&$callCount) {
            $callCount++;
            yield "stream{$callCount}";
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'multi.txt', 'text/plain');

        // First stream
        ob_start();
        $this->streamer->stream();
        $output1 = ob_get_clean();

        // Second stream (should create new generator)
        ob_start();
        $this->streamer->stream();
        $output2 = ob_get_clean();

        $this->assertEquals('stream1', $output1);
        $this->assertEquals('stream2', $output2);
        $this->assertEquals(2, $callCount);
    }

    // Test with real-world scenarios

    public function testCsvDataGeneration(): void
    {
        $generatorFunction = function() {
            yield "ID,Name,Email\n";
            for ($i = 1; $i <= 5; $i++) {
                yield "{$i},User{$i},user{$i}@example.com\n";
            }
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'data.csv', 'text/csv');

        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();

        $lines = explode("\n", trim($output));
        $this->assertCount(6, $lines); // Header + 5 data rows
        $this->assertEquals('ID,Name,Email', $lines[0]);
        $this->assertEquals('1,User1,<EMAIL>', $lines[1]);
        $this->assertEquals('5,User5,<EMAIL>', $lines[5]);
    }

    public function testJsonDataGeneration(): void
    {
        $generatorFunction = function() {
            yield '{"data":[';
            for ($i = 1; $i <= 3; $i++) {
                if ($i > 1) yield ',';
                yield json_encode(['id' => $i, 'name' => "User{$i}"]);
            }
            yield ']}';
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'data.json', 'application/json');

        ob_start();
        $this->streamer->stream();
        $output = ob_get_clean();

        $data = json_decode($output, true);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('data', $data);
        $this->assertCount(3, $data['data']);
        $this->assertEquals('User1', $data['data'][0]['name']);
        $this->assertEquals('User3', $data['data'][2]['name']);
    }

    // Test cleanup and resource management

    public function testCleanupAfterException(): void
    {
        $generatorFunction = function() {
            yield 'start';
            throw new \RuntimeException('Test exception');
        };

        $this->streamer = new GeneratorFileStreamer($generatorFunction, 'error.txt', 'text/plain');

        // Store original output buffer level
        $originalLevel = ob_get_level();

        try {
            ob_start();
            $this->streamer->stream();
            $this->fail('Expected exception was not thrown');
        } catch (\RuntimeException $e) {
            ob_end_clean();
            // Exception was expected
        }

        // Output buffer level should be restored (this is handled by FileStreamer)
        $this->assertEquals($originalLevel, ob_get_level());
    }

    protected function tearDown(): void
    {
        // Clean up any remaining output buffers
        while (ob_get_level() > 0) {
            ob_end_clean();
        }

        parent::tearDown();
    }
}
