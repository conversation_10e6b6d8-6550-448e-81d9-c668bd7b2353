<?php

namespace Nzoom\Export\Adapter;

use Exception;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * JSON export format adapter
 *
 * Handles export to JSON format with configurable formatting options and data structures
 */
class JsonExportFormatAdapter extends AbstractExportFormatAdapter
{
    /**
     * @var string Default output structure
     */
    private string $outputStructure = 'array';

    /**
     * @var bool Whether to include metadata
     */
    private bool $includeMetadata = false;

    /**
     * {@inheritdoc}
     */
    public function export($file, string $type, ExportData $exportData, array $options = []): void
    {
        try {
            // Extract JSON options from parameters
            $this->extractJsonOptions($options);

            // Validate file parameter and determine save target
            $saveTarget = $this->validateAndPrepareSaveTarget($file);

            // Validate the export type
            $type = strtolower(trim($type));
            if (!in_array($type, $this->getSupportedExtensions())) {
                throw new Exception("Unsupported export type: {$type}. Supported types: " . implode(', ', $this->getSupportedExtensions()));
            }

            // Create and write JSON content
            $this->writeJsonContent($saveTarget, $exportData, $options);

        } catch (Exception $e) {
            // Log the error
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->error('JSON export error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }

            // Re-throw the exception for caller to handle
            throw new Exception('Error generating JSON file: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Extract JSON-specific options from export options
     *
     * @param array $options Export options
     * @return void
     */
    private function extractJsonOptions(array $options): void
    {
        if (isset($options['output_structure']) && is_string($options['output_structure'])) {
            $this->outputStructure = $options['output_structure'];
        } elseif (isset($this->configuration['output_structure']) && is_string($this->configuration['output_structure'])) {
            $this->outputStructure = $this->configuration['output_structure'];
        }

        if (isset($options['include_metadata'])) {
            $this->includeMetadata = (bool) $options['include_metadata'];
        } elseif (isset($this->configuration['include_metadata'])) {
            $this->includeMetadata = (bool) $this->configuration['include_metadata'];
        }
    }

    /**
     * Write JSON content to the target
     *
     * @param string|resource $saveTarget The file path or file pointer
     * @param ExportData $exportData The export data
     * @param array $options Export options
     * @return void
     * @throws Exception If file operations fail
     */
    private function writeJsonContent($saveTarget, ExportData $exportData, array $options): void
    {
        // Prepare data structure based on output format
        $data = $this->prepareJsonData($exportData);

        // Get JSON encoding options
        $jsonOptions = $this->getJsonOptions($options);

        // Encode to JSON
        $jsonData = json_encode($data, $jsonOptions);
        if ($jsonData === false) {
            throw new Exception('Failed to encode data to JSON: ' . json_last_error_msg());
        }

        // Write to file or file pointer
        if (is_string($saveTarget)) {
            $result = file_put_contents($saveTarget, $jsonData);
            if ($result === false) {
                throw new Exception("Cannot write to file: {$saveTarget}");
            }
        } else {
            $result = fwrite($saveTarget, $jsonData);
            if ($result === false) {
                throw new Exception("Cannot write to file pointer");
            }
        }

        // Log completion
        if (isset($this->registry['logger'])) {
            $recordCount = iterator_count($exportData);
            $this->registry['logger']->info("JSON export completed: {$recordCount} records processed");
        }
    }

    /**
     * Get JSON encoding options
     *
     * @param array $options
     * @return int
     */
    private function getJsonOptions(array $options): int
    {
        $jsonOptions = 0;

        // Pretty print by default
        $prettyPrint = $options['pretty_print'] ??
                      $this->configuration['pretty_print'] ??
                      true;

        if ($prettyPrint) {
            $jsonOptions |= JSON_PRETTY_PRINT;
        }

        // Unescaped unicode
        $unescapedUnicode = $options['unescaped_unicode'] ??
                           $this->configuration['unescaped_unicode'] ??
                           true;

        if ($unescapedUnicode) {
            $jsonOptions |= JSON_UNESCAPED_UNICODE;
        }

        // Unescaped slashes
        $unescapedSlashes = $options['unescaped_slashes'] ??
                           $this->configuration['unescaped_slashes'] ??
                           true;

        if ($unescapedSlashes) {
            $jsonOptions |= JSON_UNESCAPED_SLASHES;
        }

        return $jsonOptions;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSupportedExtensions(): array
    {
        return ['json'];
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(string $format = null): string
    {
        // JSON only supports one format, so format parameter is ignored
        return 'application/json';
    }

    /**
     * {@inheritdoc}
     */
    public function getDefaultExtension(): string
    {
        return 'json';
    }

    /**
     * {@inheritdoc}
     */
    public static function supportsFormat(string $format): bool
    {
        return in_array(strtolower($format), static::getSupportedExtensions());
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatName(): string
    {
        return 'json';
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatOptions(): array
    {
        return [
            'pretty_print' => [
                'type' => 'checkbox',
                'label' => 'Pretty Print',
                'default' => true
            ],
            'unescaped_unicode' => [
                'type' => 'checkbox',
                'label' => 'Unescaped Unicode',
                'default' => true
            ],
            'unescaped_slashes' => [
                'type' => 'checkbox',
                'label' => 'Unescaped Slashes',
                'default' => true
            ]
        ];
    }
}
