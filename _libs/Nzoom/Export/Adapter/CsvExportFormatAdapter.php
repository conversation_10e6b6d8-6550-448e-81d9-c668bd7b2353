<?php

namespace Nzoom\Export\Adapter;

use Exception;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * CSV export format adapter
 *
 * Handles export to CSV format with configurable delimiters, enclosures, and encoding options
 */
class CsvExportFormatAdapter extends AbstractExportFormatAdapter
{
    /**
     * @var string Default delimiter
     */
    private string $delimiter = ',';

    /**
     * @var string Default enclosure character
     */
    private string $enclosure = '"';

    /**
     * @var string Default escape character
     */
    private string $escape = '\\';

    /**
     * @var bool Whether to include BOM for UTF-8 files
     */
    private bool $includeBom = false;

    /**
     * {@inheritdoc}
     */
    public function export($file, string $type, ExportData $exportData, array $options = []): void
    {
        try {
            // Extract CSV options from parameters
            $this->extractCsvOptions($options);

            // Validate file parameter and determine save target
            $saveTarget = $this->validateAndPrepareSaveTarget($file);

            // Validate the export type
            $type = strtolower(trim($type));
            if (!in_array($type, $this->getSupportedExtensions())) {
                throw new Exception("Unsupported export type: {$type}. Supported types: " . implode(', ', $this->getSupportedExtensions()));
            }

            // Create and write CSV content
            $this->writeCsvContent($saveTarget, $exportData);

        } catch (Exception $e) {
            // Log the error
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->error('CSV export error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }

            // Re-throw the exception for caller to handle
            throw new Exception('Error generating CSV file: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Extract CSV-specific options from export options
     *
     * @param array $options Export options
     * @return void
     */
    private function extractCsvOptions(array $options): void
    {
        $this->delimiter = $this->getDelimiter($options);

        if (isset($options['enclosure']) && is_string($options['enclosure'])) {
            $this->enclosure = $options['enclosure'];
        } elseif (isset($this->configuration['enclosure']) && is_string($this->configuration['enclosure'])) {
            $this->enclosure = $this->configuration['enclosure'];
        }

        if (isset($options['escape']) && is_string($options['escape'])) {
            $this->escape = $options['escape'];
        } elseif (isset($this->configuration['escape']) && is_string($this->configuration['escape'])) {
            $this->escape = $this->configuration['escape'];
        }

        if (isset($options['include_bom'])) {
            $this->includeBom = (bool) $options['include_bom'];
        } elseif (isset($this->configuration['include_bom'])) {
            $this->includeBom = (bool) $this->configuration['include_bom'];
        }
    }

    /**
     * Write CSV content to the target
     *
     * @param string|resource $saveTarget The file path or file pointer
     * @param ExportData $exportData The export data
     * @return void
     * @throws Exception If file operations fail
     */
    private function writeCsvContent($saveTarget, ExportData $exportData): void
    {
        // Create output stream
        if (is_string($saveTarget)) {
            $output = fopen($saveTarget, 'w');
            if ($output === false) {
                throw new Exception("Cannot open file for writing: {$saveTarget}");
            }
            $shouldCloseFile = true;
        } else {
            $output = $saveTarget; // File pointer
            $shouldCloseFile = false;
        }

        try {
            // Write BOM if requested
            if ($this->includeBom) {
                fwrite($output, "\xEF\xBB\xBF");
            }

            // Get headers from ExportData
            $header = $exportData->getHeader();
            $headers = $header->getLabels();

            // Write headers
            fputcsv($output, $headers, $this->delimiter, $this->enclosure, $this->escape);

            // Process records using streaming approach
            $this->processExportDataRecords($output, $exportData);

        } finally {
            // Close file if we opened it
            if ($shouldCloseFile && is_resource($output)) {
                fclose($output);
            }
        }
    }

    /**
     * Process ExportData records and write them to CSV
     *
     * @param resource $output The file pointer
     * @param ExportData $exportData The export data
     * @return void
     */
    private function processExportDataRecords($output, ExportData $exportData): void
    {
        $recordCount = 0;

        foreach ($exportData as $record) {
            // Process the ExportRecord and get formatted values
            $values = $this->processExportRecord($record);

            // Write CSV row
            fputcsv($output, $values, $this->delimiter, $this->enclosure, $this->escape);

            $recordCount++;

            // Perform garbage collection periodically for large exports
            if ($recordCount % 1000 === 0 && gc_enabled()) {
                gc_collect_cycles();
            }

            // Log progress for very large exports
            if (isset($this->registry['logger']) && $recordCount % 5000 === 0) {
                $this->registry['logger']->info("CSV export progress: {$recordCount} records processed");
            }
        }

        // Log completion
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->info("CSV export completed: {$recordCount} records processed");
        }
    }

    /**
     * Process a single ExportRecord and return formatted values
     *
     * @param ExportRecord $record The export record
     * @return array Array of formatted values
     */
    private function processExportRecord(ExportRecord $record): array
    {
        $values = [];
        $exportValues = $record->getValues();

        foreach ($exportValues as $exportValue) {
            $values[] = $this->formatValueForCsv($exportValue);
        }

        return $values;
    }

    /**
     * Format an ExportValue for CSV output
     *
     * @param ExportValue $exportValue The export value
     * @return string The formatted value
     */
    private function formatValueForCsv(ExportValue $exportValue): string
    {
        $value = $exportValue->getValue();
        $type = $exportValue->getType();

        // Handle null values
        if ($value === null) {
            return '';
        }

        // Format based on type
        switch ($type) {
            case ExportValue::TYPE_BOOLEAN:
                return $value ? '1' : '0';

            case ExportValue::TYPE_DATE:
            case ExportValue::TYPE_DATETIME:
                return $this->formatDateValue($value, $type);

            case ExportValue::TYPE_FLOAT:
                // Use number formatting if available
                $format = $exportValue->getFormat();
                if ($format && is_numeric($value)) {
                    return number_format((float) $value, $this->getDecimalPlaces($format), '.', '');
                }
                return (string) $value;

            case ExportValue::TYPE_INTEGER:
            case ExportValue::TYPE_STRING:
            default:
                return (string) $value;
        }
    }

    /**
     * Format date value for CSV
     *
     * @param mixed $value The date value
     * @param string $type The value type
     * @return string Formatted date string
     */
    private function formatDateValue($value, string $type): string
    {
        try {
            if ($value instanceof \DateTimeInterface) {
                return $type === ExportValue::TYPE_DATE
                    ? $value->format('Y-m-d')
                    : $value->format('Y-m-d H:i:s');
            } elseif (is_string($value) && strtotime($value) !== false) {
                $dateTime = new \DateTime($value);
                return $type === ExportValue::TYPE_DATE
                    ? $dateTime->format('Y-m-d')
                    : $dateTime->format('Y-m-d H:i:s');
            }
        } catch (\Exception $e) {
            // If date formatting fails, return original value
        }

        return (string) $value;
    }

    /**
     * Get decimal places from format string
     *
     * @param string $format The format string
     * @return int Number of decimal places
     */
    private function getDecimalPlaces(string $format): int
    {
        // Extract decimal places from format - look for digits after decimal point
        if (preg_match('/\.(\d+)/', $format, $matches)) {
            return strlen($matches[1]); // Count the digits, not convert to int
        }

        return 2; // Default to 2 decimal places
    }

    /**
     * Get delimiter from options or request
     *
     * @param array $options
     * @return string
     */
    private function getDelimiter(array $options): string
    {
        // Check options first
        if (isset($options['delimiter'])) {
            return $this->normalizeDelimiter($options['delimiter']);
        }

        // Check configuration
        if (isset($this->configuration['delimiter'])) {
            return $this->normalizeDelimiter($this->configuration['delimiter']);
        }

        // Check request
        if (isset($this->registry['request'])) {
            $delimiter = $this->registry['request']->get('delimiter', ',');
            return $this->normalizeDelimiter($delimiter);
        }

        return ',';
    }

    /**
     * Normalize delimiter string
     *
     * @param string $delimiter
     * @return string
     */
    private function normalizeDelimiter(string $delimiter): string
    {
        switch (strtolower($delimiter)) {
            case 'tab':
                return "\t";
            case 'semicolon':
                return ";";
            case 'pipe':
                return "|";
            case 'comma':
            default:
                return ",";
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function getSupportedExtensions(): array
    {
        return ['csv'];
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(string $format = null): string
    {
        // CSV only supports one format, so format parameter is ignored
        return 'text/csv';
    }

    /**
     * {@inheritdoc}
     */
    public function getDefaultExtension(): string
    {
        return 'csv';
    }

    /**
     * {@inheritdoc}
     */
    public static function supportsFormat(string $format): bool
    {
        return in_array(strtolower($format), static::getSupportedExtensions());
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatName(): string
    {
        return 'csv';
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatOptions(): array
    {
        return [
            'delimiter' => [
                'type' => 'select',
                'label' => 'Field Delimiter',
                'options' => [
                    'comma' => 'Comma (,)',
                    'semicolon' => 'Semicolon (;)',
                    'tab' => 'Tab',
                    'pipe' => 'Pipe (|)'
                ],
                'default' => 'comma'
            ],
            'enclosure' => [
                'type' => 'select',
                'label' => 'Text Enclosure',
                'options' => [
                    '"' => 'Double Quote (")',
                    "'" => "Single Quote (')",
                    '' => 'None'
                ],
                'default' => '"'
            ],
            'include_bom' => [
                'type' => 'checkbox',
                'label' => 'Include UTF-8 BOM',
                'default' => false,
                'description' => 'Add Byte Order Mark for better Excel compatibility with UTF-8 files'
            ]
        ];
    }
}
