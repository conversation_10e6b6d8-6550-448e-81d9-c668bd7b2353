<?php

namespace Nzoom\Export\Streamer;

/**
 * Generator-based streamer decorator
 *
 * This decorator wraps FileStreamer and provides generator-based data streaming.
 * It uses a generator function to provide data chunks.
 */
class GeneratorFileStreamer extends FileStreamer
{
    /**
     * @var \Generator|null The generator instance
     */
    private ?\Generator $generator = null;

    /**
     * @var callable The generator function
     */
    private $generatorFunction;

    /**
     * @var int|null Total size if known
     */
    private ?int $totalSize = null;

    /**
     * Constructor
     *
     * @param callable $generatorFunction Function that returns a generator
     * @param string $filename The filename to present to the browser
     * @param string $mimeType The MIME type for the content
     * @param int|null $totalSize Total size in bytes if known
     */
    public function __construct(callable $generatorFunction, string $filename, string $mimeType = 'application/octet-stream', ?int $totalSize = null)
    {
        parent::__construct($filename, $mimeType);

        $this->generatorFunction = $generatorFunction;
        $this->totalSize = $totalSize;

        // Set Content-Length header if total size is known
        if ($this->totalSize !== null) {
            $this->getHeaders()->addHeader('Content-Length', (string) $this->totalSize);
        }

        $this->initializeGenerator();
    }

    /**
     * Initialize the generator
     *
     * @return void
     */
    private function initializeGenerator(): void
    {
        $this->generator = call_user_func($this->generatorFunction);

        if (!($this->generator instanceof \Generator)) {
            throw new \InvalidArgumentException('Generator function must return a Generator instance');
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function performStreaming(): void
    {
        if (!$this->generator) {
            return;
        }

        while ($this->generator->valid()) {
            $chunk = $this->generator->current();

            // Convert to string if needed
            if ($chunk !== null && !is_string($chunk)) {
                $chunk = (string) $chunk;
            }

            if ($chunk !== null && $chunk !== '') {
                $this->outputChunk($chunk);
            }

            $this->generator->next();

            // Check if client disconnected
            if (!$this->isClientConnected()) {
                break;
            }
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function cleanup(): void
    {
        // Clean up generator
        if ($this->generator) {
            // Close the generator if it's still valid
            if ($this->generator->valid()) {
                try {
                    $this->generator->getReturn();
                } catch (\Exception $e) {
                    // Ignore exceptions during cleanup
                }
            }
            $this->generator = null;
        }

        // Call parent cleanup
        parent::cleanup();
    }

    /**
     * Set the total size
     *
     * @param int|null $totalSize Total size in bytes
     * @return self
     */
    public function setTotalSize(?int $totalSize): self
    {
        $this->totalSize = $totalSize;

        // Update Content-Length header
        if ($this->totalSize !== null) {
            $this->getHeaders()->addHeader('Content-Length', (string) $this->totalSize);
        }

        return $this;
    }

    /**
     * Get the total size
     *
     * @return int|null Total size in bytes, or null if unknown
     */
    public function getTotalSize(): ?int
    {
        return $this->totalSize;
    }

    /**
     * Reset the generator by re-initializing it
     *
     * @return self
     */
    public function reset(): self
    {
        $this->cleanup();
        $this->initializeGenerator();
        return $this;
    }
}
