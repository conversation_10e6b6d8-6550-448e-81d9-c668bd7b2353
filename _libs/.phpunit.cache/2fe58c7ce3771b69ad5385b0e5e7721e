a:6:{s:9:"classesIn";a:1:{s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";a:6:{s:4:"name";s:23:"JsonExportFormatAdapter";s:14:"namespacedName";s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:15;s:7:"endLine";i:514;s:7:"methods";a:19:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:30;s:7:"endLine";i:57;s:3:"ccn";i:4;}s:18:"extractJsonOptions";a:6:{s:10:"methodName";s:18:"extractJsonOptions";s:9:"signature";s:40:"extractJsonOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:65;s:7:"endLine";i:78;s:3:"ccn";i:7;}s:16:"writeJsonContent";a:6:{s:10:"methodName";s:16:"writeJsonContent";s:9:"signature";s:95:"writeJsonContent($saveTarget, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:89;s:7:"endLine";i:121;s:3:"ccn";i:6;}s:15:"prepareJsonData";a:6:{s:10:"methodName";s:15:"prepareJsonData";s:9:"signature";s:66:"prepareJsonData(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:129;s:7:"endLine";i:140;s:3:"ccn";i:5;}s:21:"prepareArrayStructure";a:6:{s:10:"methodName";s:21:"prepareArrayStructure";s:9:"signature";s:72:"prepareArrayStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:148;s:7:"endLine";i:168;s:3:"ccn";i:5;}s:22:"prepareObjectStructure";a:6:{s:10:"methodName";s:22:"prepareObjectStructure";s:9:"signature";s:73:"prepareObjectStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:176;s:7:"endLine";i:193;s:3:"ccn";i:2;}s:22:"prepareNestedStructure";a:6:{s:10:"methodName";s:22:"prepareNestedStructure";s:9:"signature";s:73:"prepareNestedStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:201;s:7:"endLine";i:224;s:3:"ccn";i:6;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:84:"processExportRecord(Nzoom\Export\Entity\ExportRecord $record, array $columns): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:233;s:7:"endLine";i:251;s:3:"ccn";i:3;}s:18:"formatValueForJson";a:6:{s:10:"methodName";s:18:"formatValueForJson";s:9:"signature";s:64:"formatValueForJson(Nzoom\Export\Entity\ExportValue $exportValue)";s:10:"visibility";s:7:"private";s:9:"startLine";i:259;s:7:"endLine";i:288;s:3:"ccn";i:9;}s:15:"formatDateValue";a:6:{s:10:"methodName";s:15:"formatDateValue";s:9:"signature";s:45:"formatDateValue($value, string $type): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:297;s:7:"endLine";i:315;s:3:"ccn";i:7;}s:11:"addMetadata";a:6:{s:10:"methodName";s:11:"addMetadata";s:9:"signature";s:75:"addMetadata(array $data, Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:324;s:7:"endLine";i:330;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:62:"getMetadata(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:338;s:7:"endLine";i:365;s:3:"ccn";i:3;}s:14:"getJsonOptions";a:6:{s:10:"methodName";s:14:"getJsonOptions";s:9:"signature";s:35:"getJsonOptions(array $options): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:373;s:7:"endLine";i:423;s:3:"ccn";i:7;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:428;s:7:"endLine";i:431;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:436;s:7:"endLine";i:440;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:445;s:7:"endLine";i:448;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:453;s:7:"endLine";i:456;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:461;s:7:"endLine";i:464;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:469;s:7:"endLine";i:513;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:515;s:18:"commentLinesOfCode";i:140;s:21:"nonCommentLinesOfCode";i:375;}s:15:"ignoredLinesFor";a:1:{i:0;i:15;}s:17:"executableLinesIn";a:206:{i:30;i:3;i:34;i:4;i:37;i:5;i:40;i:6;i:41;i:7;i:42;i:8;i:46;i:9;i:48;i:10;i:50;i:11;i:51;i:12;i:55;i:13;i:67;i:14;i:68;i:15;i:69;i:16;i:70;i:17;i:73;i:18;i:74;i:19;i:75;i:20;i:76;i:21;i:92;i:22;i:95;i:23;i:98;i:24;i:99;i:25;i:100;i:26;i:104;i:27;i:105;i:28;i:106;i:29;i:107;i:30;i:110;i:31;i:111;i:32;i:112;i:33;i:117;i:34;i:118;i:35;i:119;i:36;i:131;i:37;i:132;i:38;i:133;i:39;i:134;i:40;i:135;i:41;i:136;i:42;i:138;i:43;i:150;i:44;i:151;i:45;i:152;i:46;i:153;i:47;i:155;i:48;i:156;i:49;i:157;i:50;i:159;i:51;i:162;i:52;i:163;i:53;i:167;i:54;i:179;i:55;i:180;i:56;i:181;i:57;i:182;i:58;i:184;i:59;i:185;i:59;i:186;i:59;i:188;i:60;i:189;i:61;i:192;i:62;i:203;i:63;i:204;i:64;i:205;i:65;i:208;i:66;i:209;i:67;i:213;i:68;i:214;i:69;i:215;i:70;i:216;i:71;i:217;i:72;i:218;i:73;i:223;i:74;i:235;i:75;i:236;i:76;i:239;i:77;i:240;i:78;i:241;i:79;i:242;i:80;i:243;i:81;i:246;i:82;i:250;i:83;i:261;i:84;i:262;i:85;i:265;i:86;i:266;i:87;i:271;i:88;i:272;i:89;i:274;i:90;i:275;i:91;i:277;i:92;i:278;i:93;i:280;i:94;i:281;i:95;i:282;i:96;i:284;i:97;i:286;i:98;i:300;i:99;i:301;i:100;i:302;i:101;i:303;i:102;i:304;i:103;i:305;i:104;i:306;i:105;i:307;i:106;i:308;i:107;i:310;i:108;i:314;i:109;i:326;i:110;i:327;i:110;i:328;i:110;i:329;i:110;i:340;i:111;i:341;i:112;i:343;i:113;i:344;i:114;i:345;i:115;i:346;i:115;i:347;i:115;i:348;i:115;i:349;i:115;i:353;i:116;i:354;i:117;i:355;i:118;i:358;i:119;i:359;i:119;i:360;i:119;i:361;i:119;i:362;i:119;i:363;i:119;i:364;i:119;i:375;i:120;i:378;i:121;i:379;i:121;i:380;i:123;i:382;i:124;i:383;i:125;i:387;i:126;i:388;i:126;i:389;i:128;i:391;i:129;i:392;i:130;i:396;i:131;i:397;i:131;i:398;i:133;i:400;i:134;i:401;i:135;i:405;i:136;i:406;i:136;i:407;i:138;i:409;i:139;i:410;i:140;i:414;i:141;i:415;i:141;i:416;i:143;i:418;i:144;i:419;i:145;i:422;i:146;i:430;i:147;i:439;i:148;i:447;i:149;i:455;i:150;i:463;i:151;i:471;i:152;i:472;i:152;i:473;i:152;i:474;i:152;i:475;i:152;i:476;i:152;i:477;i:152;i:478;i:152;i:479;i:152;i:480;i:152;i:481;i:152;i:482;i:152;i:483;i:152;i:484;i:152;i:485;i:152;i:486;i:152;i:487;i:152;i:488;i:152;i:489;i:152;i:490;i:152;i:491;i:152;i:492;i:152;i:493;i:152;i:494;i:152;i:495;i:152;i:496;i:152;i:497;i:152;i:498;i:152;i:499;i:152;i:500;i:152;i:501;i:152;i:502;i:152;i:503;i:152;i:504;i:152;i:505;i:152;i:506;i:152;i:507;i:152;i:508;i:152;i:509;i:152;i:510;i:152;i:511;i:152;i:512;i:152;}}