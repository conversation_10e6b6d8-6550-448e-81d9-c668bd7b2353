a:6:{s:9:"classesIn";a:1:{s:43:"Nzoom\Export\Streamer\GeneratorFileStreamer";a:6:{s:4:"name";s:21:"GeneratorFileStreamer";s:14:"namespacedName";s:43:"Nzoom\Export\Streamer\GeneratorFileStreamer";s:9:"namespace";s:21:"Nzoom\Export\Streamer";s:9:"startLine";i:11;s:7:"endLine";i:156;s:7:"methods";a:7:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:93:"__construct(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:36;s:7:"endLine";i:49;s:3:"ccn";i:2;}s:19:"initializeGenerator";a:6:{s:10:"methodName";s:19:"initializeGenerator";s:9:"signature";s:27:"initializeGenerator(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:56;s:7:"endLine";i:63;s:3:"ccn";i:2;}s:16:"performStreaming";a:6:{s:10:"methodName";s:16:"performStreaming";s:9:"signature";s:24:"performStreaming(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:68;s:7:"endLine";i:93;s:3:"ccn";i:8;}s:7:"cleanup";a:6:{s:10:"methodName";s:7:"cleanup";s:9:"signature";s:15:"cleanup(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:98;s:7:"endLine";i:115;s:3:"ccn";i:4;}s:12:"setTotalSize";a:6:{s:10:"methodName";s:12:"setTotalSize";s:9:"signature";s:35:"setTotalSize(?int $totalSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:123;s:7:"endLine";i:133;s:3:"ccn";i:2;}s:12:"getTotalSize";a:6:{s:10:"methodName";s:12:"getTotalSize";s:9:"signature";s:20:"getTotalSize(): ?int";s:10:"visibility";s:6:"public";s:9:"startLine";i:140;s:7:"endLine";i:143;s:3:"ccn";i:1;}s:5:"reset";a:6:{s:10:"methodName";s:5:"reset";s:9:"signature";s:13:"reset(): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:150;s:7:"endLine";i:155;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:157;s:18:"commentLinesOfCode";i:58;s:21:"nonCommentLinesOfCode";i:99;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:34:{i:38;i:4;i:40;i:5;i:41;i:6;i:44;i:7;i:45;i:8;i:48;i:9;i:58;i:10;i:60;i:11;i:61;i:12;i:70;i:13;i:71;i:14;i:74;i:15;i:75;i:16;i:78;i:17;i:79;i:18;i:82;i:19;i:83;i:20;i:86;i:21;i:89;i:22;i:90;i:23;i:101;i:24;i:103;i:25;i:105;i:26;i:106;i:27;i:110;i:28;i:114;i:29;i:125;i:30;i:128;i:31;i:129;i:32;i:132;i:33;i:142;i:34;i:152;i:35;i:153;i:36;i:154;i:37;}}