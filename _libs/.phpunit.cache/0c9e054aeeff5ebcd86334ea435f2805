a:6:{s:9:"classesIn";a:1:{s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";a:6:{s:4:"name";s:23:"JsonExportFormatAdapter";s:14:"namespacedName";s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:15;s:7:"endLine";i:492;s:7:"methods";a:19:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:30;s:7:"endLine";i:57;s:3:"ccn";i:4;}s:18:"extractJsonOptions";a:6:{s:10:"methodName";s:18:"extractJsonOptions";s:9:"signature";s:40:"extractJsonOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:65;s:7:"endLine";i:78;s:3:"ccn";i:7;}s:16:"writeJsonContent";a:6:{s:10:"methodName";s:16:"writeJsonContent";s:9:"signature";s:95:"writeJsonContent($saveTarget, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:89;s:7:"endLine";i:121;s:3:"ccn";i:6;}s:15:"prepareJsonData";a:6:{s:10:"methodName";s:15:"prepareJsonData";s:9:"signature";s:66:"prepareJsonData(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:129;s:7:"endLine";i:140;s:3:"ccn";i:5;}s:21:"prepareArrayStructure";a:6:{s:10:"methodName";s:21:"prepareArrayStructure";s:9:"signature";s:72:"prepareArrayStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:148;s:7:"endLine";i:166;s:3:"ccn";i:5;}s:22:"prepareObjectStructure";a:6:{s:10:"methodName";s:22:"prepareObjectStructure";s:9:"signature";s:73:"prepareObjectStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:174;s:7:"endLine";i:185;s:3:"ccn";i:2;}s:22:"prepareNestedStructure";a:6:{s:10:"methodName";s:22:"prepareNestedStructure";s:9:"signature";s:73:"prepareNestedStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:193;s:7:"endLine";i:216;s:3:"ccn";i:6;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:68:"processExportRecord(Nzoom\Export\Entity\ExportRecord $record): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:224;s:7:"endLine";i:235;s:3:"ccn";i:2;}s:18:"formatValueForJson";a:6:{s:10:"methodName";s:18:"formatValueForJson";s:9:"signature";s:64:"formatValueForJson(Nzoom\Export\Entity\ExportValue $exportValue)";s:10:"visibility";s:7:"private";s:9:"startLine";i:243;s:7:"endLine";i:272;s:3:"ccn";i:9;}s:15:"formatDateValue";a:6:{s:10:"methodName";s:15:"formatDateValue";s:9:"signature";s:45:"formatDateValue($value, string $type): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:281;s:7:"endLine";i:299;s:3:"ccn";i:7;}s:11:"addMetadata";a:6:{s:10:"methodName";s:11:"addMetadata";s:9:"signature";s:75:"addMetadata(array $data, Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:308;s:7:"endLine";i:314;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:62:"getMetadata(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:322;s:7:"endLine";i:343;s:3:"ccn";i:2;}s:14:"getJsonOptions";a:6:{s:10:"methodName";s:14:"getJsonOptions";s:9:"signature";s:35:"getJsonOptions(array $options): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:351;s:7:"endLine";i:401;s:3:"ccn";i:7;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:406;s:7:"endLine";i:409;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:414;s:7:"endLine";i:418;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:423;s:7:"endLine";i:426;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:431;s:7:"endLine";i:434;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:439;s:7:"endLine";i:442;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:447;s:7:"endLine";i:491;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:493;s:18:"commentLinesOfCode";i:135;s:21:"nonCommentLinesOfCode";i:358;}s:15:"ignoredLinesFor";a:1:{i:0;i:15;}s:17:"executableLinesIn";a:194:{i:30;i:3;i:34;i:4;i:37;i:5;i:40;i:6;i:41;i:7;i:42;i:8;i:46;i:9;i:48;i:10;i:50;i:11;i:51;i:12;i:55;i:13;i:67;i:14;i:68;i:15;i:69;i:16;i:70;i:17;i:73;i:18;i:74;i:19;i:75;i:20;i:76;i:21;i:92;i:22;i:95;i:23;i:98;i:24;i:99;i:25;i:100;i:26;i:104;i:27;i:105;i:28;i:106;i:29;i:107;i:30;i:110;i:31;i:111;i:32;i:112;i:33;i:117;i:34;i:118;i:35;i:119;i:36;i:131;i:37;i:132;i:38;i:133;i:39;i:134;i:40;i:135;i:41;i:136;i:42;i:138;i:43;i:150;i:44;i:151;i:45;i:153;i:46;i:154;i:47;i:155;i:48;i:157;i:49;i:160;i:50;i:161;i:51;i:165;i:52;i:176;i:53;i:177;i:53;i:178;i:53;i:180;i:54;i:181;i:55;i:184;i:56;i:195;i:57;i:196;i:58;i:197;i:59;i:200;i:60;i:201;i:61;i:205;i:62;i:206;i:63;i:207;i:64;i:208;i:65;i:209;i:66;i:210;i:67;i:215;i:68;i:226;i:69;i:227;i:70;i:229;i:71;i:230;i:72;i:231;i:73;i:234;i:74;i:245;i:75;i:246;i:76;i:249;i:77;i:250;i:78;i:255;i:79;i:256;i:80;i:258;i:81;i:259;i:82;i:261;i:83;i:262;i:84;i:264;i:85;i:265;i:86;i:266;i:87;i:268;i:88;i:270;i:89;i:284;i:90;i:285;i:91;i:286;i:92;i:287;i:93;i:288;i:94;i:289;i:95;i:290;i:96;i:291;i:97;i:292;i:98;i:294;i:99;i:298;i:100;i:310;i:101;i:311;i:101;i:312;i:101;i:313;i:101;i:324;i:102;i:325;i:103;i:327;i:104;i:328;i:105;i:329;i:106;i:330;i:106;i:331;i:106;i:332;i:106;i:333;i:106;i:336;i:107;i:337;i:107;i:338;i:107;i:339;i:107;i:340;i:107;i:341;i:107;i:342;i:107;i:353;i:108;i:356;i:109;i:357;i:109;i:358;i:111;i:360;i:112;i:361;i:113;i:365;i:114;i:366;i:114;i:367;i:116;i:369;i:117;i:370;i:118;i:374;i:119;i:375;i:119;i:376;i:121;i:378;i:122;i:379;i:123;i:383;i:124;i:384;i:124;i:385;i:126;i:387;i:127;i:388;i:128;i:392;i:129;i:393;i:129;i:394;i:131;i:396;i:132;i:397;i:133;i:400;i:134;i:408;i:135;i:417;i:136;i:425;i:137;i:433;i:138;i:441;i:139;i:449;i:140;i:450;i:140;i:451;i:140;i:452;i:140;i:453;i:140;i:454;i:140;i:455;i:140;i:456;i:140;i:457;i:140;i:458;i:140;i:459;i:140;i:460;i:140;i:461;i:140;i:462;i:140;i:463;i:140;i:464;i:140;i:465;i:140;i:466;i:140;i:467;i:140;i:468;i:140;i:469;i:140;i:470;i:140;i:471;i:140;i:472;i:140;i:473;i:140;i:474;i:140;i:475;i:140;i:476;i:140;i:477;i:140;i:478;i:140;i:479;i:140;i:480;i:140;i:481;i:140;i:482;i:140;i:483;i:140;i:484;i:140;i:485;i:140;i:486;i:140;i:487;i:140;i:488;i:140;i:489;i:140;i:490;i:140;}}